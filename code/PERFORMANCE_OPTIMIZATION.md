# Shopee API 性能优化方案

## 优化概述

本次优化主要针对 `get_shopee_shop_active_items` 函数，通过引入异步并发、优化数据库操作、改进错误处理等方式，显著提升了性能。

## 主要优化点

### 1. 异步并发处理
- **问题**: 原版本所有API调用都是串行执行，造成大量等待时间
- **解决方案**: 使用 `aiohttp` 实现异步HTTP请求，并发处理多个API调用
- **性能提升**: 预计可减少 60-80% 的API调用时间

### 2. 数据库操作优化
- **问题**: 原版本使用ORM逐条操作，效率较低
- **解决方案**: 
  - 使用原生SQL批量插入
  - 增加批次大小到1000条
  - 使用 `ON DUPLICATE KEY UPDATE` 避免重复查询
- **性能提升**: 预计可减少 70-90% 的数据库操作时间

### 3. 内存使用优化
- **问题**: 大量数据在内存中处理，可能导致内存溢出
- **解决方案**:
  - 分批处理数据
  - 及时释放不需要的对象
  - 使用生成器减少内存占用

### 4. 错误处理改进
- **问题**: 单个失败会影响整个批次
- **解决方案**:
  - 使用 `asyncio.gather(return_exceptions=True)` 确保部分失败不影响整体
  - 增加详细的错误日志
  - 实现优雅降级

### 5. 代码结构优化
- **问题**: 函数过长，难以维护
- **解决方案**:
  - 拆分为多个专门的异步函数
  - 提取公共逻辑
  - 增加类型注解和文档

## 性能对比

### 预期性能提升
- **API调用时间**: 减少 60-80%
- **数据库操作时间**: 减少 70-90%
- **总体执行时间**: 减少 50-70%

### 测试方法
```bash
# 运行性能测试
python performance_test_shopee.py compare

# 单独测试同步版本
python performance_test_shopee.py sync

# 单独测试异步版本
python performance_test_shopee.py async
```

## 使用方式

### 同步版本（向后兼容）
```python
from app.scheduler_tasks.shopee_shop_no_activity_items import get_shopee_shop_active_items

result = get_shopee_shop_active_items(token, db)
```

### 异步版本（推荐）
```python
from app.scheduler_tasks.shopee_shop_no_activity_items import get_shopee_shop_active_items_async

result = await get_shopee_shop_active_items_async(token, db)
```

## 配置优化

### 批次大小调整
```python
# 在 save_monitor_records_batch 函数中
BATCH_SIZE = 1000  # 可根据数据库性能调整
```

### 并发限制
```python
# 在异步函数中可以添加并发限制
semaphore = asyncio.Semaphore(10)  # 限制并发数
```

## 监控和日志

### 性能监控
- 执行时间记录到 `performance_log.txt`
- 异步版本记录到 `async_performance_log.txt`
- 详细的错误日志输出

### 关键指标
- API调用次数和耗时
- 数据库操作次数和耗时
- 内存使用情况
- 错误率和类型

## 注意事项

1. **依赖要求**: 需要 `aiohttp` 库（已在 requirements.txt 中）
2. **数据库连接**: 确保数据库连接池配置合理
3. **内存使用**: 大数据量时注意内存监控
4. **错误处理**: 部分API失败不会影响整体执行
5. **向后兼容**: 保持原有接口不变

## 进一步优化建议

1. **缓存机制**: 对频繁访问的数据进行缓存
2. **连接池**: 优化数据库连接池配置
3. **索引优化**: 确保数据库表有合适的索引
4. **分片处理**: 对超大数据集进行分片处理
5. **监控告警**: 添加性能监控和告警机制

## 故障排除

### 常见问题
1. **内存不足**: 减少批次大小或增加内存
2. **连接超时**: 调整超时设置和重试机制
3. **数据库锁**: 优化事务范围和锁策略
4. **API限流**: 添加请求频率限制

### 调试方法
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 版本历史

- v1.0: 原始同步版本
- v2.0: 优化后的异步版本（当前）
  - 引入异步并发
  - 优化数据库操作
  - 改进错误处理
  - 增加性能监控 