#!/usr/bin/env python3
"""
深度性能分析：同步 vs 异步版本
分析为什么异步版本在没有数据库操作时比同步版本慢
"""

import time
import asyncio
import aiohttp
import requests
from concurrent.futures import ThreadPoolExecutor
import statistics
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PerformanceAnalyzer:
    def __init__(self):
        self.results = {}
    
    async def test_async_overhead(self):
        """测试异步基础开销"""
        logger.info("测试异步基础开销...")
        
        # 测试协程创建开销
        start = time.time()
        coroutines = [asyncio.sleep(0) for _ in range(1000)]
        creation_time = time.time() - start
        
        # 测试协程调度开销
        start = time.time()
        await asyncio.gather(*coroutines)
        scheduling_time = time.time() - start
        
        return {
            "coroutine_creation": creation_time,
            "coroutine_scheduling": scheduling_time,
            "total_async_overhead": creation_time + scheduling_time
        }
    
    def test_sync_overhead(self):
        """测试同步基础开销"""
        logger.info("测试同步基础开销...")
        
        # 测试函数调用开销
        start = time.time()
        for _ in range(1000):
            pass  # 空函数调用
        function_call_time = time.time() - start
        
        return {
            "function_call": function_call_time
        }
    
    async def test_http_client_performance(self, url="https://httpbin.org/delay/0.1"):
        """测试HTTP客户端性能"""
        logger.info("测试HTTP客户端性能...")
        
        # 测试aiohttp性能
        async def test_aiohttp():
            async with aiohttp.ClientSession() as session:
                start = time.time()
                tasks = []
                for _ in range(10):
                    task = session.get(url)
                    tasks.append(task)
                responses = await asyncio.gather(*tasks)
                await asyncio.gather(*[resp.release() for resp in responses])
                return time.time() - start
        
        # 测试requests性能
        def test_requests():
            start = time.time()
            with requests.Session() as session:
                for _ in range(10):
                    session.get(url)
            return time.time() - start
        
        # 测试ThreadPoolExecutor性能
        def test_threadpool():
            start = time.time()
            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(requests.get, url) for _ in range(10)]
                for future in futures:
                    future.result()
            return time.time() - start
        
        aiohttp_time = await test_aiohttp()
        requests_time = test_requests()
        threadpool_time = test_threadpool()
        
        return {
            "aiohttp": aiohttp_time,
            "requests": requests_time,
            "threadpool": threadpool_time
        }
    
    async def test_concurrency_limits(self):
        """测试不同并发限制的性能影响"""
        logger.info("测试并发限制性能影响...")
        
        url = "https://httpbin.org/delay/0.1"
        results = {}
        
        # 测试不同连接池限制
        for limit in [1, 5, 10, 20, 50]:
            connector = aiohttp.TCPConnector(limit=limit, limit_per_host=limit)
            async with aiohttp.ClientSession(connector=connector) as session:
                start = time.time()
                tasks = [session.get(url) for _ in range(20)]
                responses = await asyncio.gather(*tasks)
                await asyncio.gather(*[resp.release() for resp in responses])
                results[f"limit_{limit}"] = time.time() - start
        
        return results
    
    async def test_semaphore_impact(self):
        """测试信号量对性能的影响"""
        logger.info("测试信号量性能影响...")
        
        url = "https://httpbin.org/delay/0.1"
        results = {}
        
        # 测试不同信号量限制
        for sem_limit in [1, 5, 10, 20]:
            async with aiohttp.ClientSession() as session:
                semaphore = asyncio.Semaphore(sem_limit)
                
                async def fetch_with_semaphore():
                    async with semaphore:
                        async with session.get(url) as resp:
                            await resp.read()
                
                start = time.time()
                tasks = [fetch_with_semaphore() for _ in range(20)]
                await asyncio.gather(*tasks)
                results[f"semaphore_{sem_limit}"] = time.time() - start
        
        return results
    
    async def test_memory_usage(self):
        """测试内存使用情况"""
        logger.info("测试内存使用情况...")
        
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        
        # 测试异步内存使用
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建大量协程
        coroutines = [asyncio.sleep(0.1) for _ in range(1000)]
        await asyncio.gather(*coroutines)
        
        async_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 测试同步内存使用
        def sync_function():
            time.sleep(0.1)
        
        initial_memory_sync = process.memory_info().rss / 1024 / 1024  # MB
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(sync_function) for _ in range(1000)]
            for future in futures:
                future.result()
        
        sync_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        return {
            "async_memory_increase": async_memory - initial_memory,
            "sync_memory_increase": sync_memory - initial_memory_sync
        }
    
    async def run_comprehensive_analysis(self):
        """运行全面的性能分析"""
        logger.info("开始全面性能分析...")
        
        # 1. 基础开销测试
        async_overhead = await self.test_async_overhead()
        sync_overhead = self.test_sync_overhead()
        
        # 2. HTTP客户端性能测试
        http_performance = await self.test_http_client_performance()
        
        # 3. 并发限制测试
        concurrency_limits = await self.test_concurrency_limits()
        
        # 4. 信号量影响测试
        semaphore_impact = await self.test_semaphore_impact()
        
        # 5. 内存使用测试
        memory_usage = await self.test_memory_usage()
        
        # 汇总结果
        self.results = {
            "async_overhead": async_overhead,
            "sync_overhead": sync_overhead,
            "http_performance": http_performance,
            "concurrency_limits": concurrency_limits,
            "semaphore_impact": semaphore_impact,
            "memory_usage": memory_usage
        }
        
        return self.results
    
    def print_analysis_report(self):
        """打印分析报告"""
        print("\n" + "="*60)
        print("深度性能分析报告")
        print("="*60)
        
        # 1. 基础开销对比
        print("\n1. 基础开销对比:")
        print(f"   异步协程创建开销: {self.results['async_overhead']['coroutine_creation']:.6f}s")
        print(f"   异步协程调度开销: {self.results['async_overhead']['coroutine_scheduling']:.6f}s")
        print(f"   同步函数调用开销: {self.results['sync_overhead']['function_call']:.6f}s")
        
        # 2. HTTP客户端性能
        print("\n2. HTTP客户端性能 (10个并发请求):")
        http_perf = self.results['http_performance']
        print(f"   aiohttp: {http_perf['aiohttp']:.3f}s")
        print(f"   requests: {http_perf['requests']:.3f}s")
        print(f"   ThreadPool: {http_perf['threadpool']:.3f}s")
        
        # 3. 并发限制影响
        print("\n3. 连接池限制影响 (20个请求):")
        for limit, time_taken in self.results['concurrency_limits'].items():
            print(f"   {limit}: {time_taken:.3f}s")
        
        # 4. 信号量影响
        print("\n4. 信号量限制影响 (20个请求):")
        for sem, time_taken in self.results['semaphore_impact'].items():
            print(f"   {sem}: {time_taken:.3f}s")
        
        # 5. 内存使用
        print("\n5. 内存使用情况:")
        mem = self.results['memory_usage']
        print(f"   异步内存增加: {mem['async_memory_increase']:.2f}MB")
        print(f"   同步内存增加: {mem['sync_memory_increase']:.2f}MB")
        
        # 6. 结论和建议
        print("\n6. 结论和建议:")
        print("   - 异步版本慢的主要原因:")
        print("     * 异步基础开销较大")
        print("     * 当前并发限制过于保守")
        print("     * aiohttp在某些场景下不如requests高效")
        print("   - 优化建议:")
        print("     * 增加并发连接数限制")
        print("     * 减少信号量限制")
        print("     * 考虑使用httpx替代aiohttp")
        print("     * 对于小数据量，同步版本可能更高效")

async def main():
    """主函数"""
    analyzer = PerformanceAnalyzer()
    results = await analyzer.run_comprehensive_analysis()
    analyzer.print_analysis_report()

if __name__ == "__main__":
    asyncio.run(main()) 