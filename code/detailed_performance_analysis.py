#!/usr/bin/env python3
"""
详细的性能分析脚本
帮助诊断异步版本性能问题的原因
"""

import time
import asyncio
import logging
from datetime import datetime
from sqlalchemy.orm import Session

from app.scheduler_tasks.shopee_shop_no_activity_items import get_shopee_shop_active_items, get_shopee_shop_active_items_async
from app.model.models import SHopeeAuthToken
from app.database.database import get_db

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def analyze_sync_performance():
    """分析同步版本性能"""
    db = next(get_db())
    
    test_token = db.query(SHopeeAuthToken).first()
    if not test_token:
        logger.error("没有找到测试用的Shopee认证token")
        return
    
    logger.info(f"开始同步版本性能分析，使用shop_id: {test_token.shop_id}")
    
    # 测试同步函数
    start_time = time.time()
    try:
        result = get_shopee_shop_active_items(test_token, db)
        end_time = time.time()
        
        execution_time = end_time - start_time
        item_count = len(result)
        
        logger.info(f"同步版本性能分析结果:")
        logger.info(f"总执行时间: {execution_time:.2f} 秒")
        logger.info(f"获取商品数量: {item_count}")
        logger.info(f"平均每个商品耗时: {execution_time/max(item_count, 1):.4f} 秒")
        
        # 记录详细结果
        with open("sync_detailed_analysis.txt", "a", encoding="utf-8") as f:
            f.write(f"{datetime.now()}: 同步版本 - 执行时间={execution_time:.2f}s, 商品数量={item_count}\n")
            
    except Exception as e:
        logger.error(f"同步版本性能分析失败: {e}")
    
    db.close()


async def analyze_async_performance():
    """分析异步版本性能"""
    db = next(get_db())
    
    test_token = db.query(SHopeeAuthToken).first()
    if not test_token:
        logger.error("没有找到测试用的Shopee认证token")
        return
    
    logger.info(f"开始异步版本性能分析，使用shop_id: {test_token.shop_id}")
    
    # 测试异步函数
    start_time = time.time()
    try:
        result = await get_shopee_shop_active_items_async(test_token, db)
        end_time = time.time()
        
        execution_time = end_time - start_time
        item_count = len(result)
        
        logger.info(f"异步版本性能分析结果:")
        logger.info(f"总执行时间: {execution_time:.2f} 秒")
        logger.info(f"获取商品数量: {item_count}")
        logger.info(f"平均每个商品耗时: {execution_time/max(item_count, 1):.4f} 秒")
        
        # 记录详细结果
        with open("async_detailed_analysis.txt", "a", encoding="utf-8") as f:
            f.write(f"{datetime.now()}: 异步版本 - 执行时间={execution_time:.2f}s, 商品数量={item_count}\n")
            
    except Exception as e:
        logger.error(f"异步版本性能分析失败: {e}")
    
    db.close()


def analyze_network_performance():
    """分析网络性能"""
    import requests
    import aiohttp
    
    logger.info("开始网络性能分析...")
    
    # 测试同步HTTP请求
    sync_start = time.time()
    try:
        response = requests.get("https://partner.shopeemobile.com/api/v2/shop/auth_partner", timeout=10)
        sync_time = time.time() - sync_start
        logger.info(f"同步HTTP请求耗时: {sync_time:.3f} 秒")
    except Exception as e:
        logger.error(f"同步HTTP请求失败: {e}")
    
    # 测试异步HTTP请求
    async def test_async_http():
        async_start = time.time()
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get("https://partner.shopeemobile.com/api/v2/shop/auth_partner", timeout=10) as resp:
                    await resp.text()
            async_time = time.time() - async_start
            logger.info(f"异步HTTP请求耗时: {async_time:.3f} 秒")
        except Exception as e:
            logger.error(f"异步HTTP请求失败: {e}")
    
    asyncio.run(test_async_http())


def analyze_concurrent_limits():
    """分析并发限制对性能的影响"""
    logger.info("开始并发限制分析...")
    
    # 测试不同并发数的性能
    async def test_concurrent_performance(concurrency):
        start_time = time.time()
        
        async def dummy_task():
            await asyncio.sleep(0.1)  # 模拟网络请求
            return "done"
        
        semaphore = asyncio.Semaphore(concurrency)
        
        async def limited_task():
            async with semaphore:
                return await dummy_task()
        
        tasks = [limited_task() for _ in range(20)]
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        logger.info(f"并发数 {concurrency}: 执行时间 {execution_time:.3f} 秒")
        return execution_time
    
    # 测试不同并发数
    for concurrency in [1, 2, 5, 10, 20]:
        asyncio.run(test_concurrent_performance(concurrency))


def main():
    """主函数"""
    logger.info("开始详细性能分析...")
    
    # 1. 分析网络性能
    analyze_network_performance()
    
    # 2. 分析并发限制
    analyze_concurrent_limits()
    
    # 3. 分析同步版本性能
    analyze_sync_performance()
    
    # 4. 分析异步版本性能
    asyncio.run(analyze_async_performance())
    
    logger.info("详细性能分析完成！")


if __name__ == "__main__":
    main() 