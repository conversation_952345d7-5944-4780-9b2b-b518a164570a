## run
step1 创建python独立环境
```
cd server
python3 -m venv .
cd bin
source activate
```
step2 安装以来
```
pip3 install -r requirements.txt
```
step3 启动
```
uvicorn app.main:app --reload
```
step4 查看API & playground
```
http://127.0.0.1:8000/docs
```
## 目录结构
```
config   //配置。  
  settings.py  配置文件解析。  
database //数据库工具  
model    //模型，数据库和orm业务模型  
    models //所有数据库模型  
    schemas //所有orm  
oss      //oss工具  
routers  //各模块路由  
funsar   //语音转文本  
sumup    //摘要工具
.env.development  //开发环境变量
.env.production   //生产环境变量
tools   //杂项工具，比如：测试等临时用工具。该包会引用其他包的功能，但其他包不能使用tools中的代码。生产环境不能使用该包下的功能。
```
## 数据库更新
alembic revision --autogenerate -m "Added email to User"
alembic upgrade head

alembic current 查看当前状态
alembic upgrade head 更新到最新数据库

## 配置文件
- ENV 环境变量。默认是product，为生产环境。当前环境为development时，使用.env.development文件;生产环境使用.env.production文件
- 环境变量的解析在config/settings.py中进行
- 修改了配置文件后，请检查.env.development和.env.production文件是否一致，需要重新启动服务