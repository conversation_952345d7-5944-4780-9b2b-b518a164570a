import os
from dotenv import load_dotenv

# 获取当前环境，有development和product两种取值。默认为 product
env = os.getenv("ENV", "product")

# 构建对应的 .env 文件路径
env_file = f".env.{env}"

# 如果存在指定环境的 .env 文件，则加载它
if os.path.isfile(env_file):
    load_dotenv(dotenv_path=env_file)
else:
    # 否则尝试加载根目录下的 .env
    load_dotenv()


class Settings:
    ENV: str = os.getenv("ENV", "product")
    PORT: int = int(os.getenv("PORT", "9000"))

    # shopee配置
    SHOPEE_PARTNER_ID: int = int(os.getenv("SHOPEE_PARTNER_ID"))
    SHOPEE_PARTNER_KEY: str = os.getenv("SHOPEE_PARTNER_KEY")
    SHOPEE_HOST: str = os.getenv("SHOPEE_HOST")
    SHOPEE_FIST_TOKEN_PATH:  str = os.getenv("SHOPEE_FIST_TOKEN_PATH")
    SHOPEE_ACCESS_TOKEN_PATH: str = os.getenv("SHOPEE_ACCESS_TOKEN_PATH")

    PLATFORM_WORKSPACE_ID:  str = os.getenv("PLATFORM_WORKSPACE_ID")
    PLATFORM_API_KEY: str = os.getenv("PLATFORM_API_KEY")
    PLATFORM_AGENT_KEY: str = os.getenv("PLATFORM_AGENT_KEY")
    PLATFORM_DASH_SCOPE_BASE_URL: str = os.getenv("PLATFORM_DASH_SCOPE_BASE_URL")

    # db
    DB_HOST: str = os.getenv("DB_HOST")
    DB_PORT: int = int(os.getenv("DB_PORT"))
    DB_USER: str = os.getenv("DB_USER_NAME")
    DB_PASSWORD: str = os.getenv("DB_PASSWORD")
    DB_NAME: str = os.getenv("DB_NAME")

    # JWT
    JWT_SECRET_KEY: str = os.getenv("JWT_SECRET_KEY")
    JWT_ALGORITHM: str = os.getenv("JWT_ALGORITHM")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES"))

    # oss
    OSS_ACCESS_KEY_ID: str = os.getenv("OSS_ACCESS_KEY_ID")
    OSS_ACCESS_KEY_SECRET: str = os.getenv("OSS_ACCESS_KEY_SECRET")
    OSS_ENDPOINT: str = os.getenv("OSS_ENDPOINT")
    OSS_BUCKET_NAME: str = os.getenv("OSS_BUCKET_NAME")

    # 即梦API参数配置
    JIMENG_API_KEY: str = os.getenv("JIMENG_API_KEY")
    JIMENG_API_HOST: str = os.getenv("JIMENG_API_HOST")
    JIMENG_API_ENDPOINT: str = os.getenv("JIMENG_API_ENDPOINT")

    # 飞书
    FEISHU_APP_ID: str = os.getenv("FEISHU_APP_ID")
    FEISHU_APP_SECRET: str = os.getenv("FEISHU_APP_SECRET")

    # monitor
    MONITOR_HOST_URL = os.getenv("MONITOR_HOST_URL")

    # tiktok
    TIKTOK_CLIENT_KEY = os.getenv("TIKTOK_CLIENT_KEY")
    TIKTOK_CLIENT_SECRET = os.getenv("TIKTOK_CLIENT_SECRET")
    TIKTOK_REDIRECT_URI = os.getenv("TIKTOK_REDIRECT_URI")

settings = Settings()
