# from datetime import datetime, timedelta, timezone
# from urllib import request
#
# import requests
import uvicorn
from fastapi import FastAPI, Request, HTTPException
from contextlib import asynccontextmanager
from fastapi.responses import JSONResponse
from app.model import models, schemas
from app.database import database
from fastapi.middleware.cors import CORSMiddleware
from pydantic import ValidationError
from fastapi.exceptions import RequestValidationError
from app.model.utils import HourliveException
# from app.scheduler_tasks.shopee_voucher import check_shopee_voucher_task, check_shopee_all_shop_voucher_activity
from config.settings import settings
# from app.scheduler_tasks.scheduler import scheduler
# from app.scheduler_tasks.shopee_activity_monitor import check_shopee_discount_activity, check_shopee_voucher_activity, \
#     check_shopee_activities
# from sqlalchemy.orm import Session
import logging


# 在启动时创建所有表
@asynccontextmanager
async def lifespan(app: FastAPI):
    print("LIFESPAN STARTED")
    # 初始化数据库
    logging.info("正在初始化数据库...")
    database.init_db()
    # 启动定时任务
    # logging.info("正在启动定时任务调度器...")
    # scheduler.start()
    # 加载定时任务配置
    # logging.info("正在加载定时任务配置...")
    db = database.SessionLocal()
    # todo monitor上线后这个要去调
    # load_and_schedule_tasks(db)
    # load_monitor_voucher_task(db)
    logging.info("应用启动完成！")
    yield
    # 关闭调度器
    # logging.info("正在关闭定时任务调度器...")
    # scheduler.shutdown()
    print("LIFESPAN ENDED")


app = FastAPI(lifespan=lifespan)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# def load_monitor_voucher_task(db: Session):
#     try:
#         event = {
#             "shop_id": None,
#             "voucher_id": None,
#             "interval_seconds": 86400,
#             "job_id": "daily-check-shopee-voucher-activities",
#             "task_type": 'voucher',
#             "next_run_time": datetime.strftime(
#                 datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0), "%Y-%m-%d %H:%M:%S")
#             # "next_run_time": '2025-06-27 16:31:00'
#         }
#
#         rsp = requests.post(f'{settings.MONITOR_HOST_URL}/scheduler/upload-task', json=event)
#         if rsp.status_code != 200:
#             logging.error(f"加载监控新任务和过期任务失败: {rsp.text}")
#
#         # 用户定制任务
#         tasks = db.query(
#             models.ShopeeVoucherMonitorConfig,  # ORM对象
#             models.SHopeeAuthToken.access_token  # 附加字段
#         ).join(
#             models.SHopeeAuthToken,
#             models.ShopeeVoucherMonitorConfig.shop_id == models.SHopeeAuthToken.shop_id
#         ).filter(
#             models.ShopeeVoucherMonitorConfig.enabled == 1,
#             models.ShopeeVoucherMonitorConfig.user_subscription == 1
#         ).all()
#         current_jobs = []
#         try:
#             rsp = requests.get(f'{settings.MONITOR_HOST_URL}/scheduler/get-tasks')
#             if rsp.status_code != 200:
#                 logging.error(f"加载监控新任务和过期任务失败: {rsp.text}")
#             else:
#                 current_jobs = rsp.json().get('data', {}).get('job_ids', [])
#         except Exception as e:
#             logging.error(f"请求监控服务异常: {str(e)}")
#
#         new_jobs = ["daily-check-shopee-voucher-activities"]
#         for task, access_token in tasks:
#             job_id = f"shopee_activity_voucher_{task.shop_id}_{task.voucher_id}"
#             new_jobs.append(job_id)
#             next_run_time = task.next_run_time
#             if next_run_time is None:
#                 next_run_time = datetime.now(timezone.utc)
#             event = {
#                 "shop_id": task.shop_id,
#                 "voucher_id": task.voucher_id,
#                 "interval_seconds": task.interval_seconds,
#                 "job_id": job_id,
#                 "task_type": 'voucher',
#                 "next_run_time": datetime.strftime(next_run_time, "%Y-%m-%d %H:%M:%S")
#             }
#             try:
#                 rsp = requests.post(f'{settings.MONITOR_HOST_URL}/scheduler/upload-task', json=event)
#                 if rsp.status_code != 200:
#                     logging.error(f"加载监控新任务和过期任务失败: {rsp.text}")
#             except Exception as e:
#                 logging.error(f"请求监控服务异常: {str(e)}")
#
#         need_delete_jobs = [job_id for job_id in current_jobs if job_id not in new_jobs]
#         try:
#             rsp = requests.post(f'{settings.MONITOR_HOST_URL}/scheduler/delete-task',
#                                 json={"job_ids": need_delete_jobs})
#             if rsp.status_code != 200:
#                 logging.error(f'加载监控新任务和过期任务失败: {rsp.text}')
#         except Exception as e:
#             logging.error(f"删除过期监控服务异常: {str(e)}")
#     except Exception as e:
#         logging.error(f"加载监控新任务和过期任务时发生异常: {str(e)}")


# def load_and_schedule_tasks(db: Session):
#     """从数据库加载定时任务配置并调度"""
#     try:
#         # 加载监控新任务和过期任务
#         logging.info("正在加载监控新任务和过期任务...")
#         shopee_task_scan_job_id = f'检测shopee优惠活动任务欧列表更新--新优惠活动和过期优惠券任务'
#         scheduler.add_job(
#             check_shopee_all_shop_voucher_activity,
#             'interval',
#             seconds=86400,  # 1天一次
#             args=[shopee_task_scan_job_id],
#             id=shopee_task_scan_job_id
#         )
#         # 加载用户订阅的优惠券检测任务
#         logging.info("正在加载用户订阅的优惠券检测任务...")
#         tasks = db.query(
#             models.ShopeeVoucherMonitorConfig,  # ORM对象
#             models.SHopeeAuthToken.access_token  # 附加字段
#         ).join(
#             models.SHopeeAuthToken,
#             models.ShopeeVoucherMonitorConfig.shop_id == models.SHopeeAuthToken.shop_id
#         ).filter(
#             models.ShopeeVoucherMonitorConfig.enabled == 1,
#             models.ShopeeVoucherMonitorConfig.user_subscription == 1
#         ).all()
#         for task, access_token in tasks:
#             job_id = f"shopee_activity_voucher_{task.shop_id}_{task.voucher_id}"
#             next_run_time = task.next_run_time
#             if next_run_time is None:
#                 next_run_time = datetime.now(timezone.utc)
#             scheduler.add_job(
#                 check_shopee_voucher_task,
#                 'interval',
#                 seconds=task.interval_seconds,
#                 # seconds=600,
#                 next_run_time=next_run_time,
#                 args=[task.shop_id, task.voucher_id, access_token],
#                 id=job_id
#             )
#
#             task.next_run_time = next_run_time + timedelta(seconds=task.interval_seconds)
#         db.commit()
#         logging.info(f"已加载 {len(tasks)} 个店铺监控任务")
#
#
#
#     except Exception as e:
#         logging.error(f"加载定时任务失败: {str(e)}")
#     finally:
#         db.close()


"""
业务数据库路由
"""

from app.routers.biz_routes import ai_api_routers

app.include_router(ai_api_routers.router, prefix="/ai")

from app.routers.biz_routes import oss_api_routers

app.include_router(oss_api_routers.router, prefix="/oss")

from app.routers.biz_routes import user_api_routers

app.include_router(user_api_routers.router, prefix="/user")

from app.routers.biz_routes import app_api_routers

app.include_router(app_api_routers.router, prefix="/app")

from app.routers.biz_routes import jimeng_api_routers

app.include_router(jimeng_api_routers.router, prefix="/api")

# from app.routers.feishu_routes import feishu_api_routes
# app.include_router(feishu_api_routes.router,prefix="/feishu")

# shopee
from app.routers.biz_routes import shopee_api_routers

app.include_router(shopee_api_routers.router, prefix="/api")

# agent invocation api
from app.routers.biz_routes import agent_invocation_api_routers

app.include_router(agent_invocation_api_routers.router, prefix="/api")

# 飞书通知任务
from app.routers.biz_routes import feishu_api_routes

app.include_router(feishu_api_routes.router, prefix="/api")

# tiktok
from app.routers.biz_routes import tiktok_api_routers
app.include_router(tiktok_api_routers.router, prefix="/tts")


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        schemas.StandardResponse(success=False, message=str(exc)).dict()
    )


@app.exception_handler(HourliveException)
async def hourlive_exception_handler(request: Request, exc: HourliveException):
    return JSONResponse(
        schemas.StandardResponse(success=False, message=exc.message, status_code=exc.status_code,
                                 toast=exc.message).dict()
    )


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        schemas.StandardResponse(success=False, message=exc.detail, status_code=exc.status_code).dict()
    )


@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    return JSONResponse(
        schemas.StandardResponse(success=False, message=str(exc)).dict()
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        schemas.StandardResponse(success=False, message=str(exc)).dict()
    )


if __name__ == "__main__":
    config = uvicorn.Config(
        app=app,
        host="0.0.0.0",
        port=settings.PORT,
        # lifespan="on",
        log_level="info"
    )
    server = uvicorn.Server(config)
    server.run()
