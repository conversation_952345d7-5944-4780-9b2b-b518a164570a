import requests
from deprecated import deprecated

from fastapi import APIRouter, HTTPException, Depends, Body
from datetime import datetime, timedelta, timezone
from fastapi import Request
import aiohttp

import json

from sqlalchemy import or_
from sqlalchemy.orm import Session
from app.database.database import get_db
from app.model import models, schemas
from app.model.dependency import get_current_user
from app.model.models import SHopeeAuthToken, ShopeeTaskConfig, ShopeeShopUsers, ShopeeVoucherMonitorResult, \
    ShopeeVoucherMonitorConfig, ProductInventory, ShopeeItemMonitorResult, \
    ShopeeAccountHealthCache, ShopeeOngoingPunishmentCache, ShopeePenaltyPointHistoryCache, \
    ShopeePunishmentHistoryCache, ShopeeListingsIssuesCache, ShopeeLateOrdersCache, ShopeeMetricsCache


def determine_metric_status(metric):
    """根据metric的current_period和target确定状态"""
    current_period = metric.get("current_period")
    target = metric.get("target", {})

    if current_period is None or not target:
        return "no_data"

    target_value = target.get("value")
    comparator = target.get("comparator", "<=")

    if target_value is None:
        return "no_data"

    try:
        current_val = float(current_period)
        target_val = float(target_value)

        if comparator == "<=":
            if current_val <= target_val:
                return "excellent" if current_val <= target_val * 0.8 else "good"
            else:
                return "warning" if current_val <= target_val * 1.2 else "poor"
        elif comparator == ">=":
            if current_val >= target_val:
                return "excellent" if current_val >= target_val * 1.2 else "good"
            else:
                return "warning" if current_val >= target_val * 0.8 else "poor"
        elif comparator == "<":
            return "good" if current_val < target_val else "warning"
        elif comparator == ">":
            return "good" if current_val > target_val else "warning"
        elif comparator == "=":
            return "good" if current_val == target_val else "warning"
        else:
            return "unknown"
    except (ValueError, TypeError):
        return "no_data"
import logging
logger = logging.getLogger(__name__)
from app.scheduler_tasks.shopee_activity_monitor import get_shopee_one_shop_discount_items_async
from app.scheduler_tasks.shopee_shop_no_activity_items import get_shopee_shop_active_items_async
from app.scheduler_tasks.shopee_voucher import save_voucher_infos, \
    delete_user_shop_expired_voucher_infos, get_refresh_shopee_shops_token_info, \
    get_refresh_shopee_shop_token_info, \
    delete_db_shopee_shop_voucher_results_not_in_current_vouchers, get_shopee_voucher_info_async, \
    delete_db_shopee_shop_voucher_monitor_config_not_in_current_vouchers, get_shopee_shop_voucher_list_async
from app.utils.shopee_utils import get_access_token_shop_level
from app.scheduler_tasks.shopee_account import get_listings_with_issues, get_shopee_shop_penalty, get_shop_performance, get_metric_source_detail, get_penalty_point_history, get_punishment_history, get_late_orders

from config.settings import settings

from pydantic import BaseModel
from typing import Optional, Dict, Any, List

# from app.scheduler_tasks.shopee_activity_monitor import \
#     get_shopee_one_shop_discount_task_config_and_get_new_results
# from app.scheduler_tasks.shopee_activity_monitor import get_one_shop_voucher_task_config_and_get_new_results

from app.scheduler_tasks.scheduler import scheduler
import traceback

router = APIRouter(tags=["飞书通知接口"])


@router.get("/get_voucher_task_config", response_model=schemas.StandardResponse)
async def get_feishu_task_config(shop_id, voucher_id,
                                 current_user: models.User = Depends(get_current_user),
                                 db: Session = Depends(get_db)
                                 ):
    task_config = db.query(models.ShopeeVoucherMonitorConfig).filter(
        models.ShopeeVoucherMonitorConfig.voucher_id == voucher_id,
        models.ShopeeVoucherMonitorConfig.shop_id == shop_id
    ).first()
    if not task_config:
        return schemas.StandardResponse(success=False, message="任务配置不存在", data=None, status_code=404)

    # 解析check_rule字段
    check_rule_dict = None
    if task_config.check_rule:
        try:
            check_rule_dict = json.loads(task_config.check_rule)
        except json.JSONDecodeError:
            logger.error(f"解析check_rule失败: {task_config.check_rule}")
            check_rule_dict = None

    # 构建返回数据
    response_data = {
        "id": task_config.id,
        "shop_id": task_config.shop_id,
        "voucher_id": task_config.voucher_id,
        "interval_seconds": task_config.interval_seconds,
        "next_run_time": task_config.next_run_time.isoformat() if task_config.next_run_time else None,
        "enabled": task_config.enabled,
        "user_subscription": task_config.user_subscription,
        "check_rule": check_rule_dict,
        "create_time": task_config.create_time.isoformat() if task_config.create_time else None,
        "update_time": task_config.update_time.isoformat() if task_config.update_time else None
    }

    return schemas.StandardResponse(success=True, message="查询成功", data=response_data, status_code=200)


# @deprecated
# def add_or_update_voucher_scheduled_task(task: models.ShopeeVoucherMonitorConfig, db: Session):
#     # todo 任务调度是否按活动类别配置时间间隔？
#     job_id = f"shopee_activity_monitor_{task.shop_id}.{task.voucher_id}.voucher"
#
#     # 如果任务已存在，先删除它
#     if scheduler.get_job(job_id):
#         scheduler.remove_job(job_id)
#
#     # 构造任务参数
#     def wrapped_task():
#         from app.scheduler_tasks.shopee_voucher import get_shopee_voucher_info
#         shop_token_info = db.query(SHopeeAuthToken).filter(SHopeeAuthToken.shop_id == task.shop_id).first()
#         access_token, timestamp = get_access_token_shop_level(db, task.shop_id, settings.SHOPEE_PARTNER_ID,
#                                                               settings.SHOPEE_PARTNER_KEY, shop_token_info)
#         get_one_shop_voucher_task_config_and_get_new_results(shop_token_info, db)
#
#     # 计算下一次运行时间
#     next_run_time = task.next_run_time or datetime.now(timezone.utc) + timedelta(seconds=task.interval_seconds)
#
#     # 添加新任务
#     scheduler.add_job(
#         wrapped_task,
#         'interval',
#         seconds=task.interval_seconds,
#         id=job_id,
#         next_run_time=next_run_time,
#         replace_existing=True
#     )
#
#     return job_id


@router.post("/update_shopee_voucher_task_config", response_model=schemas.StandardResponse)
async def update_shopee_voucher_task_config(request: Request,
                                            current_user: models.User = Depends(get_current_user),
                                            db: Session = Depends(get_db)
                                            ):
    try:
        body = await request.json()
        user_id = current_user.id

        # 更新操作
        task_config = db.query(models.ShopeeVoucherMonitorConfig).filter(
            models.ShopeeVoucherMonitorConfig.voucher_id == body['voucher_id'],
            models.ShopeeShopUsers.user_id == user_id,
            models.ShopeeVoucherMonitorConfig.shop_id == models.ShopeeShopUsers.shop_id,
            models.ShopeeVoucherMonitorConfig.shop_id == body['shop_id'],
        ).first()

        if not task_config:
            raise HTTPException(detail="任务配置不存在", status_code=404)

        # 更新字段
        for key, value in body.items():
            if hasattr(task_config, key):
                # 如果字段是check_rule且值为dict，转换为JSON字符串
                if key == 'check_rule' and isinstance(value, dict):
                    setattr(task_config, key, json.dumps(value))
                else:
                    setattr(task_config, key, value)

        db.commit()
        # 调用from apscheduler.schedulers.background import BackgroundScheduler更新任务
        job_id = f"shopee-voucher-monitor-{task_config.shop_id}-{task_config.voucher_id}"
        if task_config.enabled and task_config.user_subscription == 1:
            # 调用任务调度更新函数
            ...
            headers = {"Content-Type": "application/json"}
            event = {
                "shop_id": task_config.shop_id,
                "voucher_id": task_config.voucher_id,
                "interval_seconds": task_config.interval_seconds,
                "job_id": job_id,
                "task_type": 'voucher',
            }
            response = requests.post(f'{settings.MONITOR_HOST_URL}/scheduler/upload-task', headers=headers, json=event)
            if response.status_code != 200:
                raise HTTPException(detail="更新任务失败", status_code=500)
        else:
            rsp = requests.post(f'{settings.MONITOR_HOST_URL}/scheduler/delete-task',
                                json={"job_ids": [job_id]})
            if rsp.status_code != 200:
                logging.error(f'加载监控新任务和过期任务失败: {rsp.text}')
        return schemas.StandardResponse(
            success=True,
            message="更新成功",
            data=task_config,
            status_code=200
        )


    except Exception as e:
        db.rollback()
        raise HTTPException(detail=f"操作失败: {str(e)}", status_code=500)


# @deprecated
# @router.post("/refresh_feishu_task", response_model=schemas.StandardResponse)
# async def refresh_feishu_task(request: Request,
#                               current_user: models.User = Depends(get_current_user),
#                               db: Session = Depends(get_db),
#                               activity_type: str = None,
#                               page: int = 1,
#                               page_size: int = 10,
#                               shop_ids: str = None):
#     try:
#         # 验证活动类型
#         if not activity_type or activity_type not in ['discount', 'voucher']:
#             raise HTTPException(detail="无效的活动类型，必须是 'discount' 或 'voucher'", status_code=400)
#
#         # 构建基础查询
#         query = db.query(SHopeeAuthToken).filter(
#             ShopeeShopUsers.user_id == current_user.id,
#             ShopeeShopUsers.shop_id == SHopeeAuthToken.shop_id,
#         )
#
#         # 如果提供了shop_ids，添加过滤条件
#         shop_id_list = []
#         if shop_ids:
#             try:
#                 # 将逗号分隔的字符串转换为列表
#                 shop_id_list = [shop_id.strip() for shop_id in shop_ids.split(',') if shop_id.strip()]
#                 # if shop_id_list:
#                 #     query = query.filter(SHopeeAuthToken.shop_id.in_(shop_id_list))
#             except Exception as e:
#                 logger.error(f"解析shop_ids参数失败: {str(e)}")
#                 raise HTTPException(detail="无效的shop_ids参数格式，请使用逗号分隔的shop_id列表", status_code=400)
#
#         shop_tokens = query.distinct(SHopeeAuthToken.shop_id).all()
#
#         if not shop_tokens:
#             return schemas.StandardResponse(
#                 success=True,
#                 message="未找到任何绑定的店铺",
#                 data={
#                     "items": [],
#                     "total": 0,
#                     "page": page,
#                     "page_size": page_size,
#                     "total_pages": 0
#                 },
#                 status_code=200
#             )
#
#         # 数据更新时间
#         update_time = datetime.now(timezone.utc)
#         # 更新所有店铺的活动数据
#         for token in shop_tokens:
#             # 调用折扣活动检测
#             # if activity_type == 'discount':
#             get_shopee_one_shop_discount_task_config_and_get_new_results(token, db)
#             # 调用优惠券活动检测
#             # elif activity_type == 'voucher':
#             get_one_shop_voucher_task_config_and_get_new_results(token, db)
#
#         # 计算偏移量
#         offset = (page - 1) * page_size
#         total_count = 0
#         results = []
#         # 根据活动类型查询对应的结果
#         if activity_type == 'discount':
#             # 查询折扣活动结果
#             total_count = db.query(models.ShopeeTaskResult).filter(
#                 models.ShopeeShopUsers.user_id == current_user.id,
#                 models.ShopeeShopUsers.shop_id == models.ShopeeTaskResult.shop_id,
#                 models.ShopeeTaskResult.e_commerce_activity_type == 'discount',
#                 models.ShopeeTaskResult.shop_id.in_(shop_id_list)
#             ).count()
#
#             results = db.query(models.ShopeeTaskResult, models.SHopeeAuthToken.shop_name).filter(
#                 models.ShopeeShopUsers.user_id == current_user.id,
#                 models.ShopeeShopUsers.shop_id == models.ShopeeTaskResult.shop_id,
#                 models.ShopeeTaskResult.e_commerce_activity_type == 'discount',
#                 models.SHopeeAuthToken.shop_id == models.ShopeeTaskResult.shop_id,
#                 models.ShopeeTaskResult.shop_id.in_(shop_id_list)
#             ).order_by(models.ShopeeTaskResult.update_time.desc()
#                        ).offset(offset).limit(page_size).all()
#
#         elif activity_type == 'voucher':
#             # 查询优惠券活动结果
#             total_count = db.query(models.ShopeeTaskResult).filter(
#                 models.ShopeeShopUsers.user_id == current_user.id,
#                 models.ShopeeShopUsers.shop_id == models.ShopeeTaskResult.shop_id,
#                 models.ShopeeTaskResult.e_commerce_activity_type == 'voucher',
#                 models.ShopeeTaskResult.shop_id.in_(shop_id_list)
#             ).count()
#
#             results = db.query(models.ShopeeTaskResult, models.SHopeeAuthToken.shop_name).filter(
#                 models.ShopeeShopUsers.user_id == current_user.id,
#                 models.ShopeeShopUsers.shop_id == models.ShopeeTaskResult.shop_id,
#                 models.ShopeeTaskResult.e_commerce_activity_type == 'voucher',
#                 models.SHopeeAuthToken.shop_id == models.ShopeeTaskResult.shop_id,
#                 models.ShopeeTaskResult.shop_id.in_(shop_id_list)
#             ).order_by(models.ShopeeTaskResult.update_time.desc()
#                        ).offset(offset).limit(page_size).all()
#
#         # 将 task_result 从 JSON 字符串转换为字典
#         converted_results = [
#             {
#                 **result[0].__dict__,
#                 "shop_name": result[1],  # shop_name is already a string
#                 "task_result": json.loads(result[0].task_result) if result[0].task_result else None
#             }
#             for result in results
#         ]
#
#         return schemas.StandardResponse(
#             success=True,
#             message="刷新并查询活动成功",
#             data={
#                 "items": converted_results,
#                 "total": total_count,
#                 "update_time": update_time.strftime("%Y-%m-%d %H:%M:%S"),
#                 "page": page,
#                 "page_size": page_size,
#                 "total_pages": (total_count + page_size - 1) // page_size
#             },
#             status_code=200
#         )
#
#     except Exception as e:
#         logger.error(f"刷新任务失败: {str(e)}")
#         raise HTTPException(detail=f"刷新任务失败: {str(e)}", status_code=500)


# @deprecated
# @router.get("/get_feishu_task_result", response_model=schemas.StandardResponse)
# async def get_feishu_task_result(request: Request,
#                                  current_user: models.User = Depends(get_current_user),
#                                  db: Session = Depends(get_db),
#                                  activity_type: str = None,
#                                  page: int = 1,
#                                  page_size: int = 10,
#                                  shop_ids: str = None):
#     try:
#         if not activity_type or activity_type not in ['discount', 'voucher']:
#             raise HTTPException(detail="无效的活动类型，必须是 'discount' 或 'voucher'", status_code=400)
#
#         # 构建基础查询
#         query = db.query(models.ShopeeTaskResult, models.SHopeeAuthToken.shop_name).filter(
#             models.ShopeeShopUsers.user_id == current_user.id,
#             models.ShopeeShopUsers.shop_id == models.ShopeeTaskResult.shop_id,
#             models.SHopeeAuthToken.shop_id == models.ShopeeTaskResult.shop_id,
#             models.ShopeeTaskResult.e_commerce_activity_type == activity_type
#         )
#
#         # 如果提供了shop_ids，添加过滤条件
#         if shop_ids:
#             try:
#                 # 将逗号分隔的字符串转换为列表
#                 shop_id_list = [int(shop_id.strip()) for shop_id in shop_ids.split(',') if shop_id.strip()]
#                 if shop_id_list:
#                     query = query.filter(models.ShopeeTaskResult.shop_id.in_(shop_id_list))
#             except Exception as e:
#                 logger.error(f"解析shop_ids参数失败: {str(e)}")
#                 raise HTTPException(detail="无效的shop_ids参数格式，请使用逗号分隔的shop_id列表", status_code=400)
#
#         # 获取总数
#         total_count = query.count()
#
#         # 分页查询结果
#         results = query.order_by(models.ShopeeTaskResult.update_time.desc()
#                                  ).offset((page - 1) * page_size).limit(page_size).all()
#
#         # 将 task_result 从 JSON 字符串转换为字典
#         converted_results = [
#             {
#                 **result[0].__dict__,
#                 "shop_name": result[1],  # shop_name is already a string
#                 "task_result": json.loads(result[0].task_result) if result[0].task_result else None
#             }
#             for result in results
#         ]
#
#         if results:
#             update_time = results[-1][0].update_time
#         else:
#             update_time = None
#         return schemas.StandardResponse(
#             success=True,
#             message="查询成功",
#             data={
#                 "items": converted_results,
#                 "total": total_count,
#                 "update_time": update_time.strftime("%Y-%m-%d %H:%M:%S") if update_time else None,
#                 "page": page,
#                 "page_size": page_size,
#                 "total_pages": (total_count + page_size - 1) // page_size
#             },
#             status_code=200
#         )
#
#     except Exception as e:
#         logger.error(f"查询任务结果失败: {str(e)}")
#         raise HTTPException(detail=f"查询失败: {str(e)}", status_code=500)


@router.get("/get_user_shopee_shop_info", response_model=schemas.StandardResponse)
def get_user_shopee_shop_info(current_user: models.User = Depends(get_current_user),
                              db: Session = Depends(get_db),
                              keyword=None,
                              page: int = 1,
                              page_size: int = 1000):
    try:
        # 构建基础查询
        query = db.query(SHopeeAuthToken).filter(
            ShopeeShopUsers.user_id == current_user.id,
            SHopeeAuthToken.shop_id == ShopeeShopUsers.shop_id
        )
        if keyword:
            query = query.filter(
                or_(
                    SHopeeAuthToken.shop_name.like(f"%{keyword}%"),
                    SHopeeAuthToken.shop_id.like(f"%{keyword}%")
                )
            )

        # # 获取总数
        # total_count = query.count()

        # 分页查询店铺信息
        shops = query.all()

        # 转换结果
        shop_info_list = [
            {
                "shop_id": shop.shop_id,
                "shop_name": shop.shop_name
            }
            for shop in shops
        ]

        return schemas.StandardResponse(
            success=True,
            message="查询成功",
            data={
                "items": shop_info_list
            },
            status_code=200
        )

    except Exception as e:
        logger.error(f"查询店铺信息失败: {str(e)}")
        raise HTTPException(detail=f"查询失败: {str(e)}", status_code=500)


class ShopeeShopIdsRequest(BaseModel):
    shop_ids: List[int]


@router.post("/add_user_shopee_shop_info", response_model=schemas.StandardResponse)
def add_user_shopee_shop_info(request: ShopeeShopIdsRequest,
                              current_user: models.User = Depends(get_current_user),
                              db: Session = Depends(get_db)):
    try:
        # 先删除不在请求shop_ids中的记录
        db.query(models.ShopeeShopUsers).filter(
            models.ShopeeShopUsers.user_id == current_user.id,
            ~models.ShopeeShopUsers.shop_id.in_(request.shop_ids)
        ).delete(synchronize_session=False)

        # 处理请求中的shop_ids
        for shop_id in request.shop_ids:
            # 检查是否已存在记录
            existing_record = db.query(models.ShopeeShopUsers).filter(
                models.ShopeeShopUsers.user_id == current_user.id,
                models.ShopeeShopUsers.shop_id == shop_id
            ).first()

            if existing_record:
                # 更新现有记录
                existing_record.update_time = datetime.now(timezone.utc)
            else:
                # 创建新记录
                new_record = models.ShopeeShopUsers(
                    user_id=current_user.id,
                    shop_id=shop_id,
                    create_time=datetime.now(timezone.utc),
                    update_time=datetime.now(timezone.utc)
                )
                db.add(new_record)

        db.commit()

        return schemas.StandardResponse(
            success=True,
            message="成功添加/更新店铺关联信息",
            data=None,
            status_code=200
        )
    except Exception as e:
        db.rollback()
        logger.error(f"添加/更新店铺关联信息失败: {str(e)}")
        return schemas.StandardResponse(
            success=False,
            message=f"添加/更新店铺关联信息失败: {str(e)}",
            data=None,
            status_code=500
        )


@router.get("/get_shopee_shop_info", response_model=schemas.StandardResponse)
def get_shopee_shop_info(current_user: models.User = Depends(get_current_user),
                         db: Session = Depends(get_db),
                         page: int = 1,
                         page_size: int = 100):
    try:
        # 构建基础查询
        query = db.query(SHopeeAuthToken)

        # 获取总数
        total_count = query.count()

        # 分页查询店铺信息
        shops = query.order_by(SHopeeAuthToken.shop_id).offset((page - 1) * page_size).limit(page_size).all()

        # 转换结果
        shop_info_list = [
            {
                "shop_id": shop.shop_id,
                "shop_name": shop.shop_name
            }
            for shop in shops
        ]

        return schemas.StandardResponse(
            success=True,
            message="查询成功",
            data={
                "items": shop_info_list,
                "total": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size
            },
            status_code=200
        )

    except Exception as e:
        logger.error(f"查询店铺信息失败: {str(e)}")
        raise HTTPException(detail=f"查询失败: {str(e)}", status_code=500)


@router.get("/get-shopee-shop-items-not-in-discount-by-shop-ids", response_model=schemas.StandardResponse)
async def get_shopee_shop_items_not_in_discount_by_shop_ids(shop_ids: str, refresh: bool = False,
                                                            current_user: models.User = Depends(get_current_user),
                                                            db: Session = Depends(get_db)):
    # 解析shop_ids参数
    shop_id_list = [int(sid.strip()) for sid in shop_ids.split(',') if sid.strip()]

    # 构建基础查询 - 获取所有指定店铺的token信息
    shop_tokens = db.query(SHopeeAuthToken).filter(
        ShopeeShopUsers.user_id == current_user.id,
        ShopeeShopUsers.shop_id == SHopeeAuthToken.shop_id,
        SHopeeAuthToken.shop_id.in_(shop_id_list)
    ).all()

    if not shop_tokens:
        return schemas.StandardResponse(
            success=True,
            message="未找到任何绑定的店铺",
            data={},
            status_code=200
        )

    # 批量获取所有店铺的数据库缓存数据（非刷新模式）
    all_db_items = {}
    if not refresh:
        from app.model.models import ShopeeItemMonitorResult
        shop_ids = [token.shop_id for token in shop_tokens]
        db_items = db.query(ShopeeItemMonitorResult).filter(
            ShopeeItemMonitorResult.shop_id.in_(shop_ids),
            ShopeeItemMonitorResult.total_available_stock > 0
        ).all()

        # 按店铺ID分组
        for db_item in db_items:
            if db_item.shop_id not in all_db_items:
                all_db_items[db_item.shop_id] = {}
            key = (db_item.item_id, db_item.model_id, db_item.item_name, db_item.model_name)
            all_db_items[db_item.shop_id][key] = (db_item.original_price, db_item.currency, db_item.update_time)

    # 并发处理所有店铺数据
    async def process_shop_data(shop_token):
        shop_id = shop_token.shop_id
        shop_name = shop_token.shop_name

        async with aiohttp.ClientSession() as session:
            try:
                # 获取店铺折扣活动信息（必须实时调用API）
                start_time = datetime.now()
                discount_list_result, item_model_discount_info_map = await get_shopee_one_shop_discount_items_async(
                    shop_token, db, session=session)
                end_time = datetime.now()
                print(f"获取店铺 {shop_id} 折扣活动信息耗时: {(end_time - start_time).total_seconds()}秒")

                # 根据refresh参数决定数据来源
                if refresh:
                    # 刷新模式：从API获取最新数据
                    shop_active_items = await get_shopee_shop_active_items_async(shop_token, refresh, db,
                                                                                 session=session)
                    end_time = datetime.now()
                    print(f"获取店铺 {shop_id} 活动商品信息耗时: {(end_time - start_time).total_seconds()}秒")
                else:
                    # 非刷新模式：从内存中获取已缓存的数据库数据
                    shop_active_items = all_db_items.get(shop_id, {})
                    end_time = datetime.now()
                    print(f"从内存获取店铺 {shop_id} 活动商品信息耗时: {(end_time - start_time).total_seconds()}秒")

                # 处理商品数据
                shop_active_item_model_no_price_set = set(shop_active_items.keys())
                shop_items_not_in_discount = shop_active_item_model_no_price_set - discount_list_result

                # 构建带价格信息的结果列表
                shop_items = []
                for key, value in shop_active_items.items():
                    if key in shop_items_not_in_discount:
                        item_info = {
                            "shop_id": shop_id,
                            "shop_name": shop_name,
                            "item_id": key[0],
                            "model_id": key[1],
                            "item_name": key[2],
                            "model_name": key[3],
                            "original_price": value[0],
                            "currency": value[1],
                            "discount_id": None,
                            "discount_name": None,
                            "update_time": value[2].strftime('%Y-%m-%dT%H:%M:%S') if value[2] else None,
                        }
                        item_id = key[0]
                        in_discount = item_model_discount_info_map.get(item_id)
                        if in_discount:
                            item_info["discount_name"] = in_discount[1]
                            item_info["discount_id"] = in_discount[0]
                        shop_items.append(item_info)

                return {
                    "items": shop_items,
                    "discount_list_result": discount_list_result,
                    "item_model_discount_info_map": item_model_discount_info_map
                }
            except Exception as e:
                logger.error(f"处理店铺数据时发生错误: {e}\n{traceback.format_exc()}")
                return None

    # 并发执行所有店铺的数据处理
    import asyncio
    tasks = [process_shop_data(shop_token) for shop_token in shop_tokens]
    if not tasks:
        tasks = []
    shop_results = await asyncio.gather(*tasks, return_exceptions=True)

    # 合并所有店铺的数据
    all_items = []
    all_discount_list_result = set()
    all_item_model_discount_info_map = {}

    for result in shop_results:
        if isinstance(result, dict):
            all_items.extend(result["items"])
            all_discount_list_result.update(result["discount_list_result"])
            all_item_model_discount_info_map.update(result["item_model_discount_info_map"])
        else:
            logger.error(f"处理店铺数据时发生错误: {result}")

    # 按折扣ID和商品ID排序
    sorted_items = sorted(
        all_items,
        key=lambda x: (x['discount_id'] if x['discount_id'] is not None else float('inf'), x['item_id'])
    )

    return schemas.StandardResponse(
        success=True,
        message="查询成功",
        data={
            "items": sorted_items
        },
        status_code=200
    )


@router.get("/get_shopee_shop_items_not_in_discount", response_model=schemas.StandardResponse)
async def get_shopee_shop_items_not_in_discount(shop_id, refresh: bool = False,
                                                current_user: models.User = Depends(get_current_user),
                                                db: Session = Depends(get_db)):
    # 构建基础查询
    shop_tokens = db.query(SHopeeAuthToken).filter(
        ShopeeShopUsers.user_id == current_user.id,
        ShopeeShopUsers.shop_id == SHopeeAuthToken.shop_id,
        SHopeeAuthToken.shop_id == shop_id
    ).first()
    if not shop_tokens:
        return schemas.StandardResponse(
            success=True,
            message="未找到任何绑定的店铺",
            data={},
            status_code=200
        )
    # set(item_id, model_id,item_name,model_name)，{(item_id, model_id): (discount_id, discount_name)}
    start_time = datetime.now()
    discount_list_result, item_model_discount_info_map = await get_shopee_one_shop_discount_items_async(shop_tokens, db)
    end_time = datetime.now()
    print(f"获取店铺折扣活动信息耗时: {(end_time - start_time).total_seconds()}秒")

    # 根据refresh参数决定数据来源
    if refresh:
        # 刷新模式：从API获取最新数据
        shop_active_items = await get_shopee_shop_active_items_async(shop_tokens, refresh, db)
        end_time = datetime.now()
        print(f"获取店铺活动商品信息耗时: {(end_time - start_time).total_seconds()}秒")
    else:
        # 非刷新模式：从数据库获取缓存数据
        from app.model.models import ShopeeItemMonitorResult
        db_items = db.query(ShopeeItemMonitorResult).filter(
            ShopeeItemMonitorResult.shop_id == shop_id,
            ShopeeVoucherMonitorResult.total_available_stock > 0
        ).all()

        # 转换为与API返回格式一致的数据结构
        shop_active_items = {}
        for db_item in db_items:
            key = (db_item.item_id, db_item.model_id, db_item.item_name, db_item.model_name)
            shop_active_items[key] = (db_item.original_price, db_item.currency)

        end_time = datetime.now()
        print(f"从数据库获取店铺活动商品信息耗时: {(end_time - start_time).total_seconds()}秒")
    # set(item_id, model_id,item_name,model_name)
    shop_active_item_model_no_price_set = set(shop_active_items.keys())
    shop_items_not_in_discount = shop_active_item_model_no_price_set - discount_list_result
    # 构建带价格信息的结果列表
    items = []
    for key, value in shop_active_items.items():
        if key in shop_items_not_in_discount:
            item_info = {
                "shop_id": shop_id,
                "shop_name": shop_tokens.shop_name,
                "item_id": key[0],
                "model_id": key[1],
                "item_name": key[2],
                "model_name": key[3],
                "original_price": value[0],
                "currency": value[1],
                "discount_id": None,
                "discount_name": None
            }
            item_id = key[0]
            in_discount = item_model_discount_info_map.get(item_id)
            if in_discount:
                item_info["discount_name"] = in_discount[1]
                item_info["discount_id"] = in_discount[0]
            items.append(item_info)

    sorted_items = sorted(
        items,
        key=lambda x: (x['discount_id'] if x['discount_id'] is not None else float('inf'), x['item_id'])
    )

    return schemas.StandardResponse(
        success=True,
        message="查询成功",
        data={
            "items": sorted_items
        },
        status_code=200
    )


@router.post("/refresh_shopee_user_shops_voucher_info", response_model=schemas.StandardResponse)
async def refresh_shopee_user_shops_voucher_info(page: int = 1,
                                                 page_size: int = 10,
                                                 shop_ids: str = None,
                                                 keywords: str = None,
                                                 user_subscription: int = None,
                                                 current_user: models.User = Depends(get_current_user),
                                                 db: Session = Depends(get_db),
                                                 ):
    try:
        # 解析shop_ids参数
        shop_id_list = []
        if shop_ids:
            shop_id_list = [int(sid.strip()) for sid in shop_ids.split(',') if sid.strip()]

        # 解析keywords参数
        keywords_list = []
        if keywords:
            keywords = keywords.strip()
            if keywords:
                keywords_list = keywords.split(',')
            else:
                keywords_list = []
        # 获取当前用户关联的店铺
        user_shops = db.query(ShopeeShopUsers).filter(
            ShopeeShopUsers.user_id == current_user.id
        ).all()

        user_shop_ids = [shop.shop_id for shop in user_shops]

        # 如果指定了shop_ids，则过滤出用户有权限的店铺
        authorized_shop_ids = []
        if shop_id_list:
            authorized_shop_ids = list(set(shop_id_list) & set(user_shop_ids))
            if not authorized_shop_ids:
                return schemas.StandardResponse(
                    success=False,
                    message="指定的店铺ID不在您的权限范围内",
                    data={},
                    status_code=400
                )

        # 获取用户店铺的token信息
        shop_tokens = db.query(SHopeeAuthToken).filter(
            SHopeeAuthToken.shop_id == ShopeeShopUsers.shop_id,
            ShopeeShopUsers.user_id == current_user.id
        ).all()

        if not shop_tokens:
            return schemas.StandardResponse(
                success=False,
                message="未找到店铺的认证信息",
                data={},
                status_code=400
            )

        # # 获取所有voucher信息并更新数据库
        # shop_id_voucher_ids = []

        for shop_token in shop_tokens:
            try:
                voucher_list = await get_shopee_shop_voucher_list_async(shop_token, db)
                # 保存voucher信息
                save_voucher_infos(db, voucher_list)
                voucher_ids = []
                if voucher_list:
                    for voucher_info in voucher_list:
                        voucher_ids.append(voucher_info['voucher_id'])
                # if voucher_ids:
                delete_db_shopee_shop_voucher_monitor_config_not_in_current_vouchers(db, voucher_ids,
                                                                                     shop_token.shop_id)


            except Exception as e:
                logger.error(f"获取店铺 {shop_token.shop_id} 的voucher信息失败: {str(e)}")
                continue

        # 删除过期的voucher信息
        # delete_user_shop_expired_voucher_infos(db, current_user.id, shop_id_voucher_ids)

        # 查询更新后的voucher信息
        query = db.query(ShopeeVoucherMonitorResult, ShopeeVoucherMonitorConfig.user_subscription).join(
            ShopeeVoucherMonitorConfig,
            (ShopeeVoucherMonitorResult.voucher_id == ShopeeVoucherMonitorConfig.voucher_id) &
            (ShopeeVoucherMonitorResult.shop_id == ShopeeVoucherMonitorConfig.shop_id)
        ).filter(
            ShopeeVoucherMonitorResult.shop_id.in_(authorized_shop_ids)
        )

        if user_subscription is not None and user_subscription == 1:
            query = query.filter(ShopeeVoucherMonitorConfig.user_subscription == user_subscription)

        if keywords_list:
            for keyword in keywords_list:
                query = query.filter(~ShopeeVoucherMonitorResult.voucher_code.like(f"%{keyword}%"))
        # 计算总数
        total_count = query.count()

        # 分页
        offset = (page - 1) * page_size
        voucher_records = query.offset(offset).limit(page_size).all()

        # 构建返回数据
        voucher_list = []
        for record, user_subscription in voucher_records:
            voucher_info = {
                "voucher_id": record.voucher_id,
                "voucher_name": record.voucher_name,
                "shop_id": record.shop_id,
                "voucher_code": record.voucher_code,
                "usage_quantity": record.usage_quantity,
                "current_usage": record.current_usage,
                "activity_start_time": record.activity_start_time.isoformat() if record.activity_start_time else None,
                "activity_end_time": record.activity_end_time.isoformat() if record.activity_end_time else None,
                "create_time": record.create_time.isoformat() if record.create_time else None,
                "update_time": record.update_time.isoformat() if record.update_time else None,
                "user_subscription": user_subscription
            }
            voucher_list.append(voucher_info)

        return schemas.StandardResponse(
            success=True,
            message="刷新成功",
            data={
                "vouchers": voucher_list,
                "total": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size
            },
            status_code=200
        )

    except Exception as e:
        logger.error(f"刷新voucher信息失败: {str(e)}")
        return schemas.StandardResponse(
            success=False,
            message=f"刷新失败: {str(e)}",
            data={},
            status_code=500
        )


@router.get("/get_shopee_user_shops_voucher_info", response_model=schemas.StandardResponse)
def get_shopee_user_shops_voucher_info(page: int = 1,
                                       page_size: int = 10,
                                       shop_ids: str = None,
                                       keywords: str = None,
                                       user_subscription: int = None,
                                       current_user: models.User = Depends(get_current_user),
                                       db: Session = Depends(get_db),
                                       ):
    try:
        # 解析shop_ids参数
        shop_id_list = []
        if shop_ids:
            shop_id_list = [int(sid.strip()) for sid in shop_ids.split(',') if sid.strip()]
        # 解析keywords参数
        keywords_list = []
        if keywords:
            keywords = keywords.strip()
            if keywords:
                keywords_list = keywords.split(',')
            else:
                keywords_list = []

        # 获取当前用户关联的店铺
        user_shops = db.query(ShopeeShopUsers).filter(
            ShopeeShopUsers.user_id == current_user.id
        ).all()

        user_shop_ids = [shop.shop_id for shop in user_shops]

        # 如果指定了shop_ids，则过滤出用户有权限的店铺
        if shop_id_list:
            authorized_shop_ids = list(set(shop_id_list) & set(user_shop_ids))
            if not authorized_shop_ids:
                return schemas.StandardResponse(
                    success=False,
                    message="指定的店铺ID不在您的权限范围内",
                    data={},
                    status_code=400
                )
        else:
            authorized_shop_ids = user_shop_ids
            if not authorized_shop_ids:
                return schemas.StandardResponse(
                    success=False,
                    message="您没有关联任何店铺",
                    data={},
                    status_code=400
                )

        # 查询voucher信息，关联ShopeeVoucherMonitorConfig表
        query = db.query(ShopeeVoucherMonitorResult, ShopeeVoucherMonitorConfig.user_subscription).join(
            ShopeeVoucherMonitorConfig,
            (ShopeeVoucherMonitorResult.voucher_id == ShopeeVoucherMonitorConfig.voucher_id) &
            (ShopeeVoucherMonitorResult.shop_id == ShopeeVoucherMonitorConfig.shop_id)
        ).filter(
            ShopeeVoucherMonitorResult.shop_id.in_(authorized_shop_ids)
        )

        if user_subscription is not None and user_subscription == 1:
            query = query.filter(ShopeeVoucherMonitorConfig.user_subscription == user_subscription)
        if keywords_list:
            for keyword in keywords_list:
                query = query.filter(~ShopeeVoucherMonitorResult.voucher_code.like(f"%{keyword}%"))
        # 计算总数
        total_count = query.count()

        # 分页
        offset = (page - 1) * page_size
        voucher_records = query.offset(offset).limit(page_size).all()

        # 构建返回数据
        voucher_list = []
        for record, user_subscription in voucher_records:
            voucher_info = {
                "voucher_id": record.voucher_id,
                "voucher_name": record.voucher_name,
                "shop_id": record.shop_id,
                "voucher_code": record.voucher_code,
                "usage_quantity": record.usage_quantity,
                "current_usage": record.current_usage,
                "activity_start_time": record.activity_start_time.strftime(
                    "%Y-%m-%d %H:%M:%S") if record.activity_start_time else None,
                "activity_end_time": record.activity_end_time.strftime(
                    "%Y-%m-%d %H:%M:%S") if record.activity_end_time else None,
                "create_time": record.create_time.strftime("%Y-%m-%d %H:%M:%S") if record.create_time else None,
                "update_time": record.update_time.strftime("%Y-%m-%d %H:%M:%S") if record.update_time else None,
                "user_subscription": user_subscription
            }
            voucher_list.append(voucher_info)

        return schemas.StandardResponse(
            success=True,
            message="查询成功",
            data={
                "vouchers": voucher_list,
                "total": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size
            },
            status_code=200
        )

    except Exception as e:
        logger.error(f"获取voucher信息失败: {str(e)}")
        return schemas.StandardResponse(
            success=False,
            message=f"获取voucher信息失败: {str(e)}",
            data={},
            status_code=500
        )


@router.post("/call-scheduler-task", response_model=schemas.StandardResponse)
async def call_scheduler_task_exec(request: Request, db=Depends(get_db)):
    event = await request.json()
    voucher_id = event.get('voucher_id')
    shop_id = event.get('shop_id')
    task_type = event.get('task_type')
    if task_type == 'voucher':
        shop_tokens = []
        if not shop_id and not voucher_id:
            shop_tokens = get_refresh_shopee_shops_token_info(db)
            for shop_token in shop_tokens:
                voucher_list_result = await get_shopee_shop_voucher_list_async(shop_token, db)
                shop_current_voucher_all = [voucher.get("voucher_id") for voucher in
                                            voucher_list_result]
                delete_db_shopee_shop_voucher_results_not_in_current_vouchers(db, shop_current_voucher_all,
                                                                              shop_token.shop_id)
                save_voucher_infos(db, voucher_list_result)
                delete_db_shopee_shop_voucher_monitor_config_not_in_current_vouchers(db, shop_current_voucher_all,
                                                                                     shop_token.shop_id)
        else:
            shop_token = get_refresh_shopee_shop_token_info(shop_id, db)
            if shop_token:
                shop_tokens.append(shop_token)
                voucher_result = await get_shopee_voucher_info_async(shop_id, voucher_id, shop_token.access_token)
                if voucher_result:
                    save_voucher_infos(db, [voucher_result])
    if task_type == "discount":
        shop_tokens = get_refresh_shopee_shops_token_info(db)
        for shop_token in shop_tokens:
            await get_shopee_shop_active_items_async(shop_token, refresh=True, db=db)

    return schemas.StandardResponse(success=True, data={}, message="任务更新成功", status_code=200)


@router.post("/missed_shopee_items", response_model=schemas.StandardResponse)
async def missed_shopee_items(body: dict = Body(...), db=Depends(get_db)):
    try:
        # 从body中获取product_code_list
        product_code_list = body.get("product_code_list", [])
        if not product_code_list or not isinstance(product_code_list, list):
            return schemas.StandardResponse(
                success=False,
                message="缺少或格式错误的product_code_list参数",
                data={},
                status_code=400
            )
        # 分批查存在的model_sku，最后用set差集得到缺失的code
        def chunk_list(lst, n):
            for i in range(0, len(lst), n):
                yield lst[i:i + n]
        found_codes = set()
        batch_size = 200
        for batch in chunk_list(product_code_list, batch_size):
            batch_result = db.query(ShopeeItemMonitorResult.model_sku)\
                .filter(ShopeeItemMonitorResult.model_sku.in_(batch))\
                .distinct(ShopeeItemMonitorResult.model_sku).all()
            found_codes.update(item.model_sku for item in batch_result)
        missed_codes = list(set(product_code_list) - found_codes)
        missed_codes = ['aaaa']
        return schemas.StandardResponse(
            success=True,
            message="查询成功",
            data={"missed_product_codes": missed_codes},
            status_code=200
        )
    except Exception as e:
        logger.error(f"查询缺失商品失败: {str(e)}")
        return schemas.StandardResponse(
            success=False,
            message=f"查询缺失商品失败: {str(e)}",
            data={},
            status_code=500
        )

@router.post("/shopee-stock-data", response_model=schemas.StandardResponse)
async def shopee_stock_data(body: list = Body(...), db=Depends(get_db)):
    try:
        # from datetime import datetime

        # 检查body是否为列表
        if not isinstance(body, list):
            return schemas.StandardResponse(
                success=False,
                message="body参数必须为列表",
                data={},
                status_code=400
            )

        # 批量插入数据
        records = []
        now = datetime.now(timezone.utc)
        for item in body:
            if not isinstance(item, dict):
                continue
            # 构建ProductInventory对象，自动补充created_at和updated_at
            record_data = item.copy()
            record_data["created_at"] = now
            record_data["updated_at"] = now
            record = models.ProductInventory(**record_data)
            records.append(record)
        if not records:
            return schemas.StandardResponse(
                success=False,
                message="没有有效的数据可插入",
                data={},
                status_code=400
            )
        # 先清空表中所有数据
        db.query(models.ProductInventory).delete()
        db.bulk_save_objects(records)
        db.commit()
        return schemas.StandardResponse(
            success=True,
            message="数据插入成功",
            data={},
            status_code=200
        )
    except Exception as e:
        db.rollback()
        logger.error(f"插入库存数据失败: {str(e)}")
        return schemas.StandardResponse(
            success=False,
            message=f"插入库存数据失败: {str(e)}",
            data={},
            status_code=500
        )

@router.post("/shopee-stock-data-sync", response_model=schemas.StandardResponse)
async def shopee_stock_data_sync(body: dict = Body(...), db: Session = Depends(get_db)):
    """
    查询所有店铺的SHopeeAuthToken，并对每个店铺调用get_shopee_shop_active_items_async获取活动商品信息
    """
    try:
        # 查询所有店铺的token
        shop_tokens = db.query(SHopeeAuthToken).all()
        if not shop_tokens:
            return schemas.StandardResponse(
                success=False,
                message="未找到任何店铺的授权信息",
                data={},
                status_code=404
            )
        
        async with aiohttp.ClientSession() as session:
            for token in shop_tokens:
                try:
                    await get_shopee_shop_active_items_async(token, True, db, session)
                    
                except Exception as e:
                    logger.error(f"获取店铺{token.shop_id}商品信息失败: {str(e)}")
        
        # 按missed_shopee_items函数相同的逻辑查询ShopeeItemMonitorResult表，返回所有数据
        product_code_list = []  
        # 可以考虑从body中获取product_code_list
        if isinstance(body, dict) and "product_code_list" in body:
            product_code_list = body["product_code_list"]
        if not product_code_list or not isinstance(product_code_list, list):
            return schemas.StandardResponse(
                success=False,
                message="缺少或格式错误的product_code_list参数",
                data={},
                status_code=400
            )
        # 分批查存在的model_sku，最后用set差集得到缺失的code
        def chunk_list(lst, n):
            for i in range(0, len(lst), n):
                yield lst[i:i + n]
        found_codes = set()
        batch_size = 200
        for batch in chunk_list(product_code_list, batch_size):
            batch_result = db.query(ShopeeItemMonitorResult.model_sku)\
                .filter(ShopeeItemMonitorResult.model_sku.in_(batch))\
                .distinct(ShopeeItemMonitorResult.model_sku).all()
            found_codes.update(item.model_sku for item in batch_result)
        missed_codes = list(set(product_code_list) - found_codes)
        return schemas.StandardResponse(
            success=True,
            message="查询成功",
            data={"missed_product_codes": missed_codes},
            status_code=200
        )

    except Exception as e:
        logger.error(f"查询库存数据失败: {str(e)}")
        return schemas.StandardResponse(
            success=False,
            message=f"查询库存数据失败: {str(e)}",
            data={},
            status_code=500
        )
    
# shopee中店铺无货但仓库有货的商品
@router.post("/shopee-out-of-stock-but-warehouse-available", response_model=schemas.StandardResponse)
async def shopee_out_of_stock_but_warehouse_available(body: dict = Body(...), db: Session = Depends(get_db)):
    """
    查询店铺无货但仓库有货的商品
    """
    try:
        # 从body中获取shop_ids参数
        shop_ids = body.get("shop_ids", [])
        if not shop_ids or not isinstance(shop_ids, list):
            return schemas.StandardResponse(
                success=False,
                message="缺少或格式错误的shop_ids参数",
                data={},
                status_code=400
            )

        # 查询店铺无货但仓库有货的商品
        query = db.query(
            ShopeeItemMonitorResult.shop_id,
            ShopeeItemMonitorResult.shop_name,
            ShopeeItemMonitorResult.model_sku.label('product_code'),
            ShopeeItemMonitorResult.item_name.label('product_name')
        ).join(
            models.ShopeeShopwarehouse,
            ShopeeItemMonitorResult.shop_id == models.ShopeeShopwarehouse.shop_id
        ).join(
            models.ProductInventory,
            models.ShopeeShopwarehouse.warehouse_name == models.ProductInventory.warehouse
        ).filter(
            ShopeeItemMonitorResult.shop_id.in_(shop_ids),
            ShopeeItemMonitorResult.total_available_stock == 0,
            models.ProductInventory.available_quantity > 0,
            ShopeeItemMonitorResult.model_sku == models.ProductInventory.product_code
        ).distinct()

        results = query.all()

        # 转换结果格式
        items = [
            {
                "shop_id": result.shop_id,
                "shop_name": result.shop_name,
                "product_code": result.product_code,
                "product_name": result.product_name
            }
            for result in results
        ]

        return schemas.StandardResponse(
            success=True,
            message="查询成功",
            data={"items": items},
            status_code=200
        )

    except Exception as e:
        logger.error(f"查询店铺无货但仓库有货的商品失败: {str(e)}")
        return schemas.StandardResponse(
            success=False,
            message=f"查询失败: {str(e)}",
            data={},
            status_code=500
        )

# 仓库无货店铺有货接口
@router.post("/warehouse-out-of-stock-but-shop-available", response_model=schemas.StandardResponse)
async def warehouse_out_of_stock_but_shop_available(body: dict = Body(...), db: Session = Depends(get_db)):
    """
    查询仓库无货但店铺有货的商品
    """
    try:
        # 从body中获取shop_ids参数
        shop_ids = body.get("shop_ids", [])
        if not shop_ids or not isinstance(shop_ids, list):
            return schemas.StandardResponse(
                success=False,
                message="缺少或格式错误的shop_ids参数",
                data={},
                status_code=400
            )

        # 查询仓库无货但店铺有货的商品
        query = db.query(
            ShopeeItemMonitorResult.shop_id,
            ShopeeItemMonitorResult.shop_name,
            ShopeeItemMonitorResult.model_sku.label('product_code'),
            ShopeeItemMonitorResult.item_name.label('product_name')
        ).join(
            models.ShopeeShopwarehouse,
            ShopeeItemMonitorResult.shop_id == models.ShopeeShopwarehouse.shop_id
        ).join(
            models.ProductInventory,
            models.ShopeeShopwarehouse.warehouse_name == models.ProductInventory.warehouse
        ).filter(
            ShopeeItemMonitorResult.shop_id.in_(shop_ids),
            ShopeeItemMonitorResult.total_available_stock > 0,
            models.ProductInventory.available_quantity == 0,
            ShopeeItemMonitorResult.model_sku == models.ProductInventory.product_code
        ).distinct()

        results = query.all()

        # 转换结果格式
        items = [
            {
                "shop_id": result.shop_id,
                "shop_name": result.shop_name,
                "product_code": result.product_code,
                "product_name": result.product_name
            }
            for result in results
        ]

        return schemas.StandardResponse(
            success=True,
            message="查询成功",
            data={"items": items},
            status_code=200
        )

    except Exception as e:
        logger.error(f"查询仓库无货但店铺有货的商品失败: {str(e)}")
        return schemas.StandardResponse(
            success=False,
            message=f"查询失败: {str(e)}",
            data={},
            status_code=500
        )

# 店铺商品异常列表接口
@router.post("/shopee-shop-items-with-issues", response_model=schemas.StandardResponse)
async def shopee_shop_items_with_issues(body: dict = Body(...), db: Session = Depends(get_db)):
    """
    查询店铺商品异常列表 - 直接调用Shopee API获取最新数据
    """
    try:
        # 从body中获取shop_ids参数
        shop_ids = body.get("shop_ids", [])
        if not shop_ids or not isinstance(shop_ids, list):
            return schemas.StandardResponse(
                success=False,
                message="缺少或格式错误的shop_ids参数",
                data={},
                status_code=400
            )

        # 获取所有店铺的token信息
        shop_tokens = db.query(SHopeeAuthToken).filter(
            SHopeeAuthToken.shop_id.in_(shop_ids)
        ).all()

        if not shop_tokens:
            return schemas.StandardResponse(
                success=False,
                message="未找到任何店铺的授权信息",
                data={},
                status_code=404
            )

        all_items = []

        # 为每个店铺直接调用get_listings_with_issues函数获取最新数据
        for token in shop_tokens:
            try:
                # 获取最新的access_token
                access_token = get_access_token_shop_level(
                    db, token.shop_id, settings.SHOPEE_PARTNER_ID,
                    settings.SHOPEE_PARTNER_KEY, token
                )

                # 直接调用get_listings_with_issues函数获取最新数据
                listings_data = get_listings_with_issues(token.shop_id, access_token)
                listings_with_issues = listings_data.get("listing_list", [])

                # 转换数据格式
                for item in listings_with_issues:
                    all_items.append({
                        "shop_id": token.shop_id,
                        "shop_name": token.shop_name or "",
                        "item_id": item.get("item_id", ""),
                        "item_name": item.get("item_name", ""),
                        "model_id": item.get("model_id", ""),
                        "model_name": item.get("model_name", ""),
                        "issue_type": item.get("reason", ""),
                        "issue_description": item.get("reason_description", "")
                    })

            except Exception as e:
                logger.error(f"获取店铺{token.shop_id}商品异常列表失败: {str(e)}")
                continue

        return schemas.StandardResponse(
            success=True,
            message="查询成功",
            data={"items": all_items},
            status_code=200
        )

    except Exception as e:
        logger.error(f"查询店铺商品异常列表失败: {str(e)}")
        return schemas.StandardResponse(
            success=False,
            message=f"查询失败: {str(e)}",
            data={},
            status_code=500
        )


def save_account_health_to_cache(db: Session, shop_id: int, shop_name: str, data: dict):
    """保存账户健康数据到缓存表"""
    try:
        # 1. 删除该shop_id的所有旧记录
        db.query(ShopeeAccountHealthCache).filter(ShopeeAccountHealthCache.shop_id == shop_id).delete()
        db.query(ShopeeOngoingPunishmentCache).filter(ShopeeOngoingPunishmentCache.shop_id == shop_id).delete()
        db.query(ShopeePenaltyPointHistoryCache).filter(ShopeePenaltyPointHistoryCache.shop_id == shop_id).delete()
        db.query(ShopeePunishmentHistoryCache).filter(ShopeePunishmentHistoryCache.shop_id == shop_id).delete()
        db.query(ShopeeListingsIssuesCache).filter(ShopeeListingsIssuesCache.shop_id == shop_id).delete()
        db.query(ShopeeLateOrdersCache).filter(ShopeeLateOrdersCache.shop_id == shop_id).delete()
        db.query(ShopeeMetricsCache).filter(ShopeeMetricsCache.shop_id == shop_id).delete()

        # 2. 保存主表数据
        penalty_info = data.get("penalty_info", {})
        performance_overview = data.get("performance_overview", {})
        penalty_details = data.get("penalty_details", {})
        overall_performance = performance_overview.get("overall_performance", {})

        penalty_points_data = penalty_info.get("penalty_points", {})
        main_cache = ShopeeAccountHealthCache(
            shop_id=shop_id,
            shop_name=shop_name,
            overall_penalty_points=int(penalty_points_data.get("overall_penalty_points", 0)),
            non_fulfillment_rate_points=int(penalty_points_data.get("non_fulfillment_rate", 0)),
            late_shipment_rate_points=int(penalty_points_data.get("late_shipment_rate", 0)),
            listing_violations_points=int(penalty_points_data.get("listing_violations", 0)),
            opfr_violations_points=int(penalty_points_data.get("opfr_violations", 0)),
            others_points=int(penalty_points_data.get("others", 0)),
            has_ongoing_punishment=penalty_info.get("has_ongoing_punishment", False),
            ongoing_punishment_count=len(penalty_info.get("ongoing_punishment", [])),
            overall_rating=int(overall_performance.get("rating")) if overall_performance.get("rating") is not None else None,
            overall_rating_description=str(overall_performance.get("rating_description", "")),
            fulfillment_failed=int(overall_performance.get("fulfillment_failed", 0)),
            listing_failed=int(overall_performance.get("listing_failed", 0)),
            custom_service_failed=int(overall_performance.get("custom_service_failed", 0)),
            metric_count=performance_overview.get("metric_count", 0),
            performance_last_updated=None,  # 暂时设为None，避免时间解析错误
            penalty_point_history_count=len(penalty_details.get("penalty_point_history", [])),
            punishment_history_count=len(penalty_details.get("punishment_history", [])),
            listings_with_issues_count=len(penalty_details.get("listings_with_issues", [])),
            late_orders_count=len(penalty_details.get("late_orders", []))
        )
        db.add(main_cache)

        # 3. 保存正在进行的处罚
        for punishment in penalty_info.get("ongoing_punishment", []):
            punishment_cache = ShopeeOngoingPunishmentCache(
                shop_id=shop_id,
                # 新字段（符合API文档）
                punishment_name=punishment.get("punishment_name"),
                punishment_tier=int(punishment.get("punishment_tier", 0)),  # int32类型
                days_left=int(punishment.get("days_left", 0)),
                # 兼容旧字段
                punishment_type=punishment.get("punishment_name"),
                punishment_id=str(punishment.get("punishment_tier", "")),
                start_time=None,
                end_time=None,
                reason=f"Tier {punishment.get('punishment_tier', '')}",
                severity=str(punishment.get("punishment_tier", "")),
                description=f"Days left: {punishment.get('days_left', 0)}"
            )
            db.add(punishment_cache)

        # 4. 保存处罚分历史（仅在有处罚时才有这个数据）
        for record in penalty_details.get("penalty_point_history", []):
            penalty_point_cache = ShopeePenaltyPointHistoryCache(
                shop_id=shop_id,
                issue_time=datetime.fromisoformat(record.get("issue_time", "").replace('Z', '+00:00')) if record.get("issue_time") else None,
                latest_point_num=record.get("latest_point_num", 0),
                original_point_num=record.get("original_point_num", 0),
                reference_id=record.get("reference_id"),
                violation_type=record.get("violation_type"),
                violation_type_description=record.get("violation_type_description")
            )
            db.add(penalty_point_cache)

        # 5. 保存处罚历史（仅在有处罚时才有这个数据）
        for record in penalty_details.get("punishment_history", []):
            punishment_history_cache = ShopeePunishmentHistoryCache(
                shop_id=shop_id,
                issue_time=datetime.fromisoformat(record.get("issue_time", "").replace('Z', '+00:00')) if record.get("issue_time") else None,
                start_time=datetime.fromisoformat(record.get("start_time", "").replace('Z', '+00:00')) if record.get("start_time") else None,
                end_time=datetime.fromisoformat(record.get("end_time", "").replace('Z', '+00:00')) if record.get("end_time") else None,
                punishment_type=record.get("punishment_type"),
                punishment_type_description=record.get("punishment_type_description"),
                reason=record.get("reason"),
                reason_description=record.get("reason_description"),
                reference_id=record.get("reference_id"),
                listing_limit=record.get("listing_limit"),
                order_limit=record.get("order_limit")
            )
            db.add(punishment_history_cache)

        # 6. 保存问题商品（总是获取，无论是否有处罚）
        for item in penalty_details.get("listings_with_issues", []):
            listing_issue_cache = ShopeeListingsIssuesCache(
                shop_id=shop_id,
                item_id=item.get("item_id"),
                item_name=None,  # API文档中没有item_name字段
                model_id=None,   # API文档中没有model_id字段
                model_name=None, # API文档中没有model_name字段
                detailed_reason=item.get("reason"),  # API文档中是reason字段
                detailed_reason_description=item.get("reason_description"),
                update_time=None  # API文档中没有update_time字段
            )
            db.add(listing_issue_cache)

        # 7. 保存延迟订单（总是获取，无论是否有处罚）
        for order in penalty_details.get("late_orders", []):
            late_order_cache = ShopeeLateOrdersCache(
                shop_id=shop_id,
                order_sn=order.get("order_sn"),
                shipping_deadline=datetime.fromisoformat(order.get("shipping_deadline", "").replace('Z', '+00:00')) if order.get("shipping_deadline") else None,
                actual_shipping_time=None,  # API文档中没有actual_shipping_time字段
                late_by_days=order.get("late_by_days", 0)
            )
            db.add(late_order_cache)

        # 8. 保存指标详情
        for metric in data.get("metrics", []):
            # 处理target字段
            target_info = metric.get("target", {})
            target_value = None
            target_comparator = None

            if isinstance(target_info, dict):
                target_value = target_info.get("value")
                target_comparator = target_info.get("comparator")
            elif isinstance(target_info, (int, float)):
                target_value = target_info
                target_comparator = "="

            # 处理score字段，确保是整数
            score = metric.get("score")
            if score is None or score == "":
                score = 0
            else:
                score = int(score)

            # 处理status字段，空字符串转为None
            status = metric.get("status")
            if status == "":
                status = None

            # 处理detail字段，确保包含正确的metric_id/metrics_id映射
            detail_data = metric.get("detail", {})
            if isinstance(detail_data, dict) and "metrics_id" in detail_data:
                # 确保detail中的metrics_id与metric_id一致
                detail_data["metric_id"] = detail_data.get("metrics_id", metric.get("metric_id"))

            metric_cache = ShopeeMetricsCache(
                shop_id=shop_id,
                metric_id=int(metric.get("metric_id")),
                metric_name=metric.get("metric_name"),
                score=score,
                target_value=float(target_value) if target_value is not None else None,
                target_comparator=target_comparator,
                target_json=json.dumps(target_info, ensure_ascii=False) if target_info else None,
                status=status,
                detail=json.dumps(detail_data, ensure_ascii=False) if detail_data else None
            )
            db.add(metric_cache)

        db.commit()
        logger.info(f"成功保存店铺{shop_id}的账户健康数据到缓存")

    except Exception as e:
        db.rollback()
        logger.error(f"保存店铺{shop_id}账户健康数据到缓存失败: {str(e)}")
        raise


def get_account_health_from_cache(db: Session, shop_id: int) -> dict:
    """从缓存表获取账户健康数据"""
    try:
        # 1. 获取主表数据
        main_cache = db.query(ShopeeAccountHealthCache).filter(ShopeeAccountHealthCache.shop_id == shop_id).first()
        if not main_cache:
            return None

        # 2. 获取正在进行的处罚
        ongoing_punishments = db.query(ShopeeOngoingPunishmentCache).filter(ShopeeOngoingPunishmentCache.shop_id == shop_id).all()

        # 3. 获取处罚分历史
        penalty_point_history = db.query(ShopeePenaltyPointHistoryCache).filter(ShopeePenaltyPointHistoryCache.shop_id == shop_id).all()

        # 4. 获取处罚历史
        punishment_history = db.query(ShopeePunishmentHistoryCache).filter(ShopeePunishmentHistoryCache.shop_id == shop_id).all()

        # 5. 获取问题商品
        listings_with_issues = db.query(ShopeeListingsIssuesCache).filter(ShopeeListingsIssuesCache.shop_id == shop_id).all()

        # 6. 获取延迟订单
        late_orders = db.query(ShopeeLateOrdersCache).filter(ShopeeLateOrdersCache.shop_id == shop_id).all()

        # 7. 获取指标详情
        metrics = db.query(ShopeeMetricsCache).filter(ShopeeMetricsCache.shop_id == shop_id).all()

        # 8. 组装返回数据
        result = {
            "shop_id": main_cache.shop_id,
            "shop_name": main_cache.shop_name,
            "penalty_info": {
                "penalty_points": {
                    "overall_penalty_points": main_cache.overall_penalty_points,
                    "non_fulfillment_rate": main_cache.non_fulfillment_rate_points,
                    "late_shipment_rate": main_cache.late_shipment_rate_points,
                    "listing_violations": main_cache.listing_violations_points,
                    "opfr_violations": main_cache.opfr_violations_points,
                    "others": main_cache.others_points
                },
                "ongoing_punishment": [
                    {
                        "punishment_name": p.punishment_name,  # 直接使用新字段
                        "punishment_tier": p.punishment_tier,  # 直接使用新字段(int32)
                        "days_left": p.days_left  # 直接使用新字段
                    } for p in ongoing_punishments
                ],
                "has_ongoing_punishment": main_cache.has_ongoing_punishment
            },
            "performance_overview": {
                "overall_performance": {
                    "rating": main_cache.overall_rating,
                    "rating_description": main_cache.overall_rating_description,
                    "fulfillment_failed": main_cache.fulfillment_failed,
                    "listing_failed": main_cache.listing_failed,
                    "custom_service_failed": main_cache.custom_service_failed
                },
                "metric_count": main_cache.metric_count,
                "last_updated": main_cache.performance_last_updated.isoformat() + 'Z' if main_cache.performance_last_updated else None
            },

            # 指标详情
            "metrics": [
                {
                    "metric_id": m.metric_id,
                    "metric_name": m.metric_name,
                    "score": m.score,
                    "target": json.loads(m.target_json) if m.target_json else {
                        "value": m.target_value,
                        "comparator": m.target_comparator
                    } if m.target_value is not None else {},
                    "status": m.status,
                    "detail": json.loads(m.detail) if m.detail else {}
                } for m in metrics
            ],

            # 状态汇总
            "health_summary": {
                "status": "warning" if main_cache.has_ongoing_punishment else "normal",
                "total_penalty_points": main_cache.overall_penalty_points
            }
        }

        # 8. 如果有处罚详情数据，添加penalty_details
        if penalty_point_history or punishment_history or listings_with_issues or late_orders:
            result["penalty_details"] = {
                "penalty_point_history": [
                    {
                        "issue_time": p.issue_time.isoformat() + 'Z' if p.issue_time else None,
                        "latest_point_num": p.latest_point_num,
                        "original_point_num": p.original_point_num,
                        "reference_id": p.reference_id,
                        "violation_type": p.violation_type,
                        "violation_type_description": p.violation_type_description
                    } for p in penalty_point_history
                ],
                "punishment_history": [
                    {
                        "issue_time": p.issue_time.isoformat() + 'Z' if p.issue_time else None,
                        "start_time": p.start_time.isoformat() + 'Z' if p.start_time else None,
                        "end_time": p.end_time.isoformat() + 'Z' if p.end_time else None,
                        "punishment_type": p.punishment_type,
                        "punishment_type_description": p.punishment_type_description,
                        "reason": p.reason,
                        "reason_description": p.reason_description,
                        "reference_id": p.reference_id,
                        "listing_limit": p.listing_limit,
                        "order_limit": p.order_limit
                    } for p in punishment_history
                ],
                "listings_with_issues": [
                    {
                        "item_id": item.item_id,
                        "reason": item.detailed_reason,  # 映射回reason字段
                        "reason_description": item.detailed_reason_description
                    } for item in listings_with_issues
                ],
                "late_orders": [
                    {
                        "order_sn": order.order_sn,
                        "shipping_deadline": order.shipping_deadline.isoformat() + 'Z' if order.shipping_deadline else None,
                        "late_by_days": order.late_by_days
                    } for order in late_orders
                ]
            }

        return result

    except Exception as e:
        logger.error(f"从缓存获取店铺{shop_id}账户健康数据失败: {str(e)}")
        return None


# 店铺账户健康综合监控接口
@router.post("/shopee-shop-account-health", response_model=schemas.StandardResponse)
async def shopee_shop_account_health(body: dict = Body(...), db: Session = Depends(get_db)):
    """
    获取店铺账户健康综合信息
    包括：处罚状态、表现指标、指标详情等
    """
    try:
        # 从body中获取shop_ids参数
        shop_ids = body.get("shop_ids", [])
        if not shop_ids or not isinstance(shop_ids, list):
            return schemas.StandardResponse(
                success=False,
                message="缺少或格式错误的shop_ids参数",
                data={},
                status_code=400
            )

        # 获取refresh参数，默认为False
        refresh = body.get("refresh", False)

        # 获取是否需要详细指标信息的参数
        include_metric_details = body.get("include_metric_details", True)

        # # ========== 临时测试数据 - 前端测试完成后删除 ==========
        # test_data = []
        # for i, shop_id in enumerate(shop_ids):
        #     # 第一个店铺有处罚，其他店铺正常
        #     has_punishment = (i == 0)
        #
        #     test_shop_data = {
        #         "shop_id": shop_id,
        #         "shop_name": f"测试店铺_{shop_id}",
        #
        #         # 处罚信息
        #         "penalty_info": {
        #             "penalty_points": {
        #                 "overall_penalty_points": 8 if has_punishment else 2,
        #                 "non_fulfillment_rate": 2 if has_punishment else 0,
        #                 "late_shipment_rate": 3 if has_punishment else 1,
        #                 "listing_violations": 1 if has_punishment else 0,
        #                 "opfr_violations": 1 if has_punishment else 0,
        #                 "others": 1 if has_punishment else 1
        #             },
        #             "ongoing_punishment": [
        #                 {
        #                     "punishment_name": "deboost",
        #                     "punishment_tier": 3,
        #                     "days_left": 16
        #                 },
        #                 {
        #                     "punishment_name": "listing_ban",
        #                     "punishment_tier": 2,
        #                     "days_left": 7
        #                 }
        #             ] if has_punishment else [],
        #             "has_ongoing_punishment": has_punishment
        #         },
        #
        #         # 店铺表现概览
        #         "performance_overview": {
        #             "overall_performance": {
        #                 "rating": 2 if has_punishment else 3,
        #                 "rating_description": "ImprovementNeeded" if has_punishment else "Good",
        #                 "fulfillment_failed": 2 if has_punishment else 0,
        #                 "listing_failed": 1 if has_punishment else 0,
        #                 "custom_service_failed": 0
        #             },
        #             "metric_count": 15,
        #             "last_updated": "2024-01-20T10:30:00Z"
        #         },
        #
        #         # 指标详情
        #         "metrics": [
        #             {
        #                 "metric_id": 1,
        #                 "metric_name": "Late Shipment Rate (All Channels)",
        #                 "score": 85,
        #                 "target": 90,
        #                 "status": "warning",
        #                 "detail": {
        #                     "metric_id": 1,
        #                     "lsr_order_list": [
        #                         {
        #                             "order_sn": "SH240118001",
        #                             "shipping_deadline": 1705449599,
        #                             "late_by_days": 3
        #                         },
        #                         {
        #                             "order_sn": "SH240117005",
        #                             "shipping_deadline": 1705363199,
        #                             "late_by_days": 2
        #                         }
        #                     ],
        #                     "total_count": 2
        #                 }
        #             },
        #             {
        #                 "metric_id": 3,
        #                 "metric_name": "Non-fulfilment Rate (All Channels)",
        #                 "score": 92,
        #                 "target": 95,
        #                 "status": "good",
        #                 "detail": {
        #                     "metric_id": 3,
        #                     "nfr_order_list": [
        #                         {
        #                             "order_sn": "SH240115003",
        #                             "non_fulfillment_type": 2,
        #                             "non_fulfillment_type_description": "Seller Cancellation",
        #                             "detailed_reason": 10005,
        #                             "detailed_reason_description": "Out of Stock"
        #                         }
        #                     ],
        #                     "total_count": 1
        #                 }
        #             },
        #             {
        #                 "metric_id": 12,
        #                 "metric_name": "Customer Service Response Rate",
        #                 "score": 98,
        #                 "target": 95,
        #                 "status": "excellent",
        #                 "detail": {
        #                     "metric_id": 12,
        #                     "total_count": 0
        #                 }
        #             }
        #         ],
        #
        #         # 状态汇总
        #         "health_summary": {
        #             "status": "warning" if has_punishment else "normal",
        #             "total_penalty_points": 8 if has_punishment else 2
        #         }
        #     }
        #
        #     # 处罚详情（仅在有正在进行的处罚时存在）
        #     if has_punishment:
        #         test_shop_data["penalty_details"] = {
        #             "penalty_point_history": [
        #                 {
        #                     "issue_time": "2024-01-18T08:00:00Z",
        #                     "latest_point_num": 10,
        #                     "original_point_num": 10,
        #                     "reference_id": 764539404640322244,
        #                     "violation_type": 5,
        #                     "violation_type_description": "High Late Shipment Rate"
        #                 },
        #                 {
        #                     "issue_time": "2024-01-15T09:30:00Z",
        #                     "latest_point_num": 15,
        #                     "original_point_num": 15,
        #                     "reference_id": 764539404640322245,
        #                     "violation_type": 9,
        #                     "violation_type_description": "Prohibited Listings"
        #                 },
        #                 {
        #                     "issue_time": "2024-01-10T14:20:00Z",
        #                     "latest_point_num": 0,
        #                     "original_point_num": 5,
        #                     "reference_id": 764539404640322246,
        #                     "violation_type": 6,
        #                     "violation_type_description": "High Non-fulfilment Rate"
        #                 }
        #             ],
        #             "punishment_history": [
        #                 {
        #                     "issue_time": "2024-01-15T09:30:00Z",
        #                     "start_time": "2024-01-15T00:00:00Z",
        #                     "end_time": "2024-01-22T23:59:59Z",
        #                     "punishment_type": 104,
        #                     "punishment_type_description": "Listings not displayed in search",
        #                     "reason": 2,
        #                     "reason_description": "Tier 2",
        #                     "reference_id": 764539404640322247,
        #                     "listing_limit": None,
        #                     "order_limit": None
        #                 },
        #                 {
        #                     "issue_time": "2024-01-18T08:00:00Z",
        #                     "start_time": "2024-01-18T00:00:00Z",
        #                     "end_time": "2024-01-25T23:59:59Z",
        #                     "punishment_type": 1109,
        #                     "punishment_type_description": "Listing Limit is reduced",
        #                     "reason": 1109,
        #                     "reason_description": "Listing Limit Tier 1",
        #                     "reference_id": 764539404640322248,
        #                     "listing_limit": 100,
        #                     "order_limit": None
        #                 },
        #                 {
        #                     "issue_time": "2024-01-05T14:20:00Z",
        #                     "start_time": "2024-01-05T00:00:00Z",
        #                     "end_time": "2024-01-10T23:59:59Z",
        #                     "punishment_type": 2008,
        #                     "punishment_type_description": "Order Limit",
        #                     "reason": 3,
        #                     "reason_description": "Tier 3",
        #                     "reference_id": 764539404640322249,
        #                     "listing_limit": None,
        #                     "order_limit": "80%"
        #                 }
        #             ],
        #             "listings_with_issues": [
        #                 {
        #                     "item_id": 987654321,
        #                     "reason": 1,
        #                     "reason_description": "Prohibited"
        #                 },
        #                 {
        #                     "item_id": 987654322,
        #                     "reason": 5,
        #                     "reason_description": "Insufficient Info"
        #                 },
        #                 {
        #                     "item_id": 987654323,
        #                     "reason": 2,
        #                     "reason_description": "Counterfeit"
        #                 }
        #             ],
        #             "late_orders": [
        #                 {
        #                     "order_sn": "SH240118001",
        #                     "shipping_deadline": "2024-01-16T23:59:59Z",
        #                     "late_by_days": 3
        #                 },
        #                 {
        #                     "order_sn": "SH240117005",
        #                     "shipping_deadline": "2024-01-15T23:59:59Z",
        #                     "late_by_days": 2
        #                 },
        #                 {
        #                     "order_sn": "SH240116008",
        #                     "shipping_deadline": "2024-01-17T23:59:59Z",
        #                     "late_by_days": 4
        #                 },
        #                 {
        #                     "order_sn": "SH240114012",
        #                     "shipping_deadline": "2024-01-16T23:59:59Z",
        #                     "late_by_days": 2
        #                 }
        #             ]
        #         }
        #
        #     test_data.append(test_shop_data)
        #
        # # 返回测试数据
        # return schemas.StandardResponse(
        #     success=True,
        #     message="success (测试数据)",
        #     data={
        #         "shops": test_data,
        #         "summary": {
        #             "total_shops": len(test_data),
        #             "shops_with_punishment": len([s for s in test_data if s.get("penalty_info", {}).get("has_ongoing_punishment", False)])
        #         }
        #     },
        #     status_code=200
        # )
        # # ========== 测试数据结束 ==========

        # 获取所有店铺的token信息
        shop_tokens = db.query(SHopeeAuthToken).filter(
            SHopeeAuthToken.shop_id.in_(shop_ids)
        ).all()

        if not shop_tokens:
            return schemas.StandardResponse(
                success=False,
                message="未找到任何店铺的授权信息",
                data={},
                status_code=404
            )

        shops_health_data = []

        # 为每个店铺获取账户健康信息
        for token in shop_tokens:
            try:
                shop_id = token.shop_id
                shop_name = token.shop_name or f"店铺{shop_id}"

                if refresh:
                    # refresh=True: 调用Shopee API获取实时数据并保存到缓存
                    logger.info(f"店铺{shop_id}执行refresh=True逻辑，调用Shopee API")

                    # 获取最新的access_token
                    access_token, timestamp = get_access_token_shop_level(
                        db, shop_id, settings.SHOPEE_PARTNER_ID,
                        settings.SHOPEE_PARTNER_KEY, token
                    )

                    # 1. 获取店铺处罚信息（核心）
                    shop_penalty = get_shopee_shop_penalty(shop_id, access_token)

                    # 2. 获取店铺表现指标
                    shop_performance = get_shop_performance(shop_id, access_token)

                    # 3. 检查是否有正在进行的处罚
                    ongoing_punishment = shop_penalty.get("ongoing_punishment", [])
                    has_ongoing_punishment = len(ongoing_punishment) > 0

                    # 4. 获取详细的问题分析信息
                    penalty_details = {}
                    try:
                        # 总是获取问题商品列表和延迟订单（这些是问题的根源）
                        listings_data = get_listings_with_issues(shop_id, access_token)
                        penalty_details["listings_with_issues"] = listings_data.get("listing_list", [])

                        late_orders_data = get_late_orders(shop_id, access_token)
                        penalty_details["late_orders"] = late_orders_data.get("late_order_list", [])

                        # 只有在有正在进行的处罚时，才获取处罚相关的历史数据
                        if has_ongoing_punishment:
                            # 获取处罚分历史 - 现在返回单个对象
                            penalty_point_history_data = get_penalty_point_history(shop_id, access_token)
                            penalty_details["penalty_point_history"] = penalty_point_history_data.get("penalty_point_list", [])

                            # 获取处罚历史 - 现在返回单个对象
                            punishment_history_data = get_punishment_history(shop_id, access_token)
                            penalty_details["punishment_history"] = punishment_history_data.get("punishment_list", [])

                            logger.info(f"店铺{shop_id}有正在进行的处罚，已获取完整的处罚详细信息")
                        else:
                            logger.info(f"店铺{shop_id}无正在进行的处罚，已获取问题商品和延迟订单信息")

                    except Exception as e:
                        logger.error(f"获取店铺{shop_id}详细信息失败: {str(e)}")
                        penalty_details["error"] = f"获取详细信息失败: {str(e)}"

                    # 5. 处理指标详情（可选）
                    metric_details = []
                    if include_metric_details and shop_performance:
                        metric_list = shop_performance.get("metric_list", [])
                        # 只获取重要指标的详情
                        important_metrics = [1, 3, 12, 15, 25, 28, 42, 43, 52, 53, 85, 88, 91, 92, 96, 97, 2001, 2002, 2003]

                        for metric in metric_list:
                            metric_id = metric.get("metric_id", "")
                            if metric_id in important_metrics:
                                try:
                                    # 获取指标详情
                                    metric_source_detail = get_metric_source_detail(shop_id, access_token, metric_id)

                                    # 根据API文档构建正确的metric结构
                                    # 使用current_period作为score，因为这是实际的性能数值
                                    score_value = metric.get("current_period")
                                    if score_value is None:
                                        score_value = 0

                                    # 构建与实际API结构一致的metrics数据
                                    metric_details.append({
                                        "metric_id": metric_id,
                                        "metric_name": metric.get("metric_name", ""),
                                        "score": score_value,
                                        "target": metric.get("target", {}),
                                        "status": determine_metric_status(metric),
                                        "detail": metric_source_detail  # get_metric_source_detail返回的完整对象
                                    })
                                except Exception as e:
                                    logger.error(f"获取指标{metric_id}详情失败: {str(e)}")
                                    continue

                    # 6. 构建返回数据
                    shop_health = {
                        "shop_id": shop_id,
                        "shop_name": shop_name,

                        # 处罚信息（重点）
                        "penalty_info": {
                            "penalty_points": shop_penalty.get("penalty_points", {}),
                            "ongoing_punishment": ongoing_punishment,
                            "has_ongoing_punishment": has_ongoing_punishment
                        },

                        # 店铺表现概览
                        "performance_overview": {
                            "overall_performance": shop_performance.get("overall_performance", {}),
                            "metric_count": len(shop_performance.get("metric_list", [])),
                            "last_updated": None  # API不返回此字段，设为None
                        },

                        # 指标详情（可选）
                        "metrics": metric_details if include_metric_details else [],

                        # 状态汇总
                        "health_summary": {
                            "status": "warning" if has_ongoing_punishment else "normal",
                            "total_penalty_points": shop_penalty.get("penalty_points", {}).get("overall_penalty_points", 0)
                        }
                    }

                    # 添加详细的问题分析信息（总是包含，但内容根据是否有处罚而不同）
                    shop_health["penalty_details"] = penalty_details

                    # 7. 保存数据到缓存
                    try:
                        save_account_health_to_cache(db, shop_id, shop_name, shop_health)
                        logger.info(f"店铺{shop_id}数据已保存到缓存")
                    except Exception as e:
                        logger.error(f"保存店铺{shop_id}数据到缓存失败: {str(e)}")

                    shops_health_data.append(shop_health)

                else:
                    # refresh=False: 直接从缓存获取数据
                    logger.info(f"店铺{shop_id}执行refresh=False逻辑，从缓存获取数据")

                    cached_data = get_account_health_from_cache(db, shop_id)
                    if cached_data:
                        shops_health_data.append(cached_data)
                        logger.info(f"店铺{shop_id}从缓存获取数据成功")
                    else:
                        # 缓存中没有数据，返回提示信息
                        shops_health_data.append({
                            "shop_id": shop_id,
                            "shop_name": shop_name,
                            "error": "No cached data found. Please set refresh=true first.",
                            "health_summary": {
                                "status": "no_data"
                            }
                        })
                        logger.warning(f"店铺{shop_id}缓存中没有数据")

            except Exception as e:
                logger.error(f"获取店铺{token.shop_id}账户健康信息失败: {str(e)}")
                # 添加错误信息但继续处理其他店铺
                shops_health_data.append({
                    "shop_id": token.shop_id,
                    "shop_name": token.shop_name or "",
                    "error": f"获取数据失败: {str(e)}",
                    "health_summary": {
                        "status": "error"
                    }
                })
                continue

        return schemas.StandardResponse(
            success=True,
            message="查询成功",
            data={
                "shops": shops_health_data,
                "summary": {
                    "total_shops": len(shops_health_data),
                    "shops_with_punishment": len([s for s in shops_health_data if s.get("penalty_info", {}).get("has_ongoing_punishment", False)])
                }
            },
            status_code=200
        )

    except Exception as e:
        logger.error(f"查询店铺账户健康信息失败: {str(e)}")
        return schemas.StandardResponse(
            success=False,
            message=f"查询失败: {str(e)}",
            data={},
            status_code=500
        )



# 延迟订单
@router.post("/shopee-late-orders", response_model=schemas.StandardResponse)
async def shopee_late_orders(body: dict = Body(...), db: Session = Depends(get_db)):
    """
    查询店铺延迟订单列表 - 直接调用Shopee API获取最新数据
    """
    try:
        # 从body中获取shop_ids参数
        shop_ids = body.get("shop_ids", [])
        if not shop_ids or not isinstance(shop_ids, list):
            return schemas.StandardResponse(
                success=False,
                message="缺少或格式错误的shop_ids参数",
                data={},
                status_code=400
            )

        # 获取所有店铺的token信息
        shop_tokens = db.query(SHopeeAuthToken).filter(
            SHopeeAuthToken.shop_id.in_(shop_ids)
        ).all()

        if not shop_tokens:
            return schemas.StandardResponse(
                success=False,
                message="未找到任何店铺的授权信息",
                data={},
                status_code=404
            )

        all_orders = []

        # 为每个店铺直接调用get_late_orders函数获取最新数据
        for token in shop_tokens:
            try:
                # 获取最新的access_token
                access_token = get_access_token_shop_level(
                    db, token.shop_id, settings.SHOPEE_PARTNER_ID,
                    settings.SHOPEE_PARTNER_KEY, token
                )

                # 直接调用get_late_orders函数获取最新数据
                late_orders_data = get_late_orders(token.shop_id, access_token)
                late_orders = late_orders_data.get("late_order_list", [])

                # 转换数据格式
                for order in late_orders:
                    all_orders.append({
                        "shop_id": token.shop_id,
                        "shop_name": token.shop_name or "",
                        "order_sn": order.get("order_sn", ""),
                        "shipping_deadline": order.get("shipping_deadline", ""),
                        "late_by_days": order.get("late_by_days", 0),
                    })
            except Exception as e:
                logger.error(f"获取店铺{token.shop_id}延迟订单失败: {str(e)}")
                continue

        return schemas.StandardResponse(
            success=True,
            message="查询成功",
            data={"orders": all_orders},
            status_code=200
        )

    except Exception as e:
        logger.error(f"查询店铺延迟订单列表失败: {str(e)}")
        return schemas.StandardResponse(
            success=False,
            message=f"查询失败: {str(e)}",
            data={},
            status_code=500
        )
