from email.policy import default

from sqlalchemy import Table, Column, Integer, String, ForeignKey, DateTime, Boolean, Text, Float, UniqueConstraint, \
    DECIMAL, BigInteger
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.orm import relationship, backref
from datetime import datetime,timezone
from typing import List
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()

# user_task = Table('user_task', Base.metadata,
#     Column('user_id', Integer, ForeignKey('user.id')),
#     Column('task_id', Integer, ForeignKey('task.id'))
# )

##################### 用户相关 #####################
class User(Base):
    __tablename__ = 'user'
    
    id = Column(Integer, primary_key=True, index=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    account = Column(String(50), unique=True, index=True)
    hashed_password = Column(String(256))
    user_type = Column(Integer, default=0) # 0普通用户 1机构主体 2顾客主体 8管理员 3主播streamer
    app_key = Column(String(256), nullable=True)

    #外键
    role_id = Column(Integer, ForeignKey('role.id'), nullable=True)

    #用户信息
    name = Column(String(100), nullable=True)
    avatar = Column(String(200), nullable=True)
    mobile = Column(String(50), nullable=True)
    email = Column(String(100), nullable=True)

    tasks = relationship("Task", backref = backref('user')) #一对多
    
class Role(Base):
    __tablename__ = 'role'
    
    id = Column(Integer, primary_key=True, index=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    name = Column(String(50), unique=True)
    
    users = relationship("User", backref = backref('role')) #一对多
    auths = relationship("Auth", backref = backref('role')) #一对多

class Auth(Base):
    __tablename__ = 'auth'
    
    id = Column(Integer, primary_key=True, index=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    name = Column(String(50), unique=True)
    code = Column(String(50))
    
    #外键
    role_id = Column(Integer, ForeignKey('role.id'))

##################### 应用相关 #####################

class Application(Base):
    __tablename__ = 'application'
    
    id = Column(Integer, primary_key=True, index=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    name = Column(String(100))
    app_id = Column(String(100)) # 百炼智能体应用ID
    description = Column(String(200))
    status = Column(Integer, default=0) # 0 未发布 1 已发布 2 会员可见
    type = Column(Integer, default=0) # 0 独立应用 1 pipeline应用
    logo = Column(String(200))

    # 任务
    tasks = relationship("Task", backref = backref('application')) #一对多

class Task(Base):
    __tablename__ = 'task'
    
    id = Column(Integer, primary_key=True, index=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    status = Column(Integer, default=0) # 0 运行中 1 已完成 2 失败

    # 重试任务
    retry_id = Column(Integer, ForeignKey('task.id'), nullable=True)

    # 应用 - 从某个应用发起
    application_id = Column(Integer, ForeignKey('application.id'))

    # 用户 - 属于某个用户
    user_id = Column(Integer, ForeignKey('user.id'))

    # 会话
    conversations = relationship("Conversation", backref = backref('task')) #一对多

    # 花销
    cost_token = Column(Float, default=0)
    cost_price = Column(Float, default=0)

class Conversation(Base):
    __tablename__ = 'conversation'
    
    id = Column(Integer, primary_key=True, index=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    # 属于某个任务
    task_id = Column(Integer, ForeignKey('task.id'))

    #inputs
    input = relationship("Input", backref = backref('conversation'), uselist=False) #一对一

    #outputs
    output = relationship("Output", backref = backref('conversation'), uselist=False) #一对一
    
class Input(Base):
    __tablename__ = 'input'
    
    id = Column(Integer, primary_key=True, index=True)

    input_type = Column(Integer, default=0,comment="0:文本, 1: 图片, 2: 语音, 3: 视频") # 0 文本 1 图片 2 语音 3 视频
    input_content = Column(Text)

    # 属于某个会话
    conversation_id = Column(Integer, ForeignKey('conversation.id'))

class Output(Base):
    __tablename__ = 'output'
    
    id = Column(Integer, primary_key=True, index=True)
    output_type = Column(Integer, default=0,comment="0:文本, 1: 图片, 2: 语音, 3: 视频") # 0 文本 1 图片 2 语音 3 视频
    output_content = Column(Text)

    # 属于某个会话
    conversation_id = Column(Integer, ForeignKey('conversation.id'))
    

##################### 智能体名称：aaa 智能体appid:bbb #####################
# class Product(Base):
#     __tablename__ = 'product'
    
#     id = Column(Integer, primary_key=True, index=True)
#     create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
#     update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))


##################### 业务相关，智能体appid:xxx #####################

class ItemTextMaterial(Base):
    __tablename__ = 'item_text_material'

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(50), nullable=False)
    product_title = Column(String(255), nullable=False)
    marking_name = Column(String(50))
    marketing_plan = Column(Text)
    product_details = Column(Text)

class ItemModelImg(Base):
    __tablename__ = 'item_model_img'
    
    task_id = Column(String(50), ForeignKey('item_text_material.task_id'), primary_key=True)
    marketing_name = Column(String(50))
    model_img_name = Column(String(100))
    model_img = Column(String(100))
    model_img_prompt = Column(String(300))
    status = Column(String(20), default='pending')

class SHopeeAuthToken(Base):
    __tablename__ = 'shopee_auth_token'
    id = Column(Integer, primary_key=True, index=True)
    shop_id = Column(Integer, nullable=True)
    access_token = Column(String(100), nullable=False)
    refresh_token = Column(String(100), nullable=False)
    expire_in = Column(Integer, nullable=True)
    main_account_id = Column(Integer, nullable=True)
    merchant_id = Column(Integer, nullable=True)
    supplier_id = Column(Integer, nullable=True)
    shop_name = Column(String(200), nullable=False)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

class ShopeeShopUsers(Base):
    __tablename__ = 'shopee_shop_users'
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('user.id'))
    shop_id = Column(Integer, ForeignKey('shopee_auth_token.shop_id'))
    # 用户关联字段
    notify_platform_receiver_id = Column(String(100), nullable=False)  # 用户飞书ID
    notify_platform_receiver_type = Column(Integer, nullable=False, comment="接收者类型:1-用户个人,2-群组")
    notify_platform_receiver_name = Column(String(200), nullable=True)

    notify_content_type = Column(Integer, default=1, nullable=False, comment="通知内容类型:1-text,2-interactive")
    notify_enabled = Column(Integer, default=1, comment="是否通知:0-否,1-是")  # 是否开启通知
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

class ShopeeTaskConfig(Base):
    __tablename__ = "shopee_task_configs"
    id = Column(Integer, primary_key=True, index=True)
    # user_id = Column(Integer, ForeignKey("user.id"))
    task_name = Column(String(200))  # 店铺名称
    shop_id = Column(Integer, unique=True)  # 店铺ID，唯一
    interval_seconds = Column(Integer)  # 执行间隔（秒）
    next_run_time = Column(DateTime)  # 下次执行时间
    e_commerce_activity_type = Column(String(100), nullable=False, default="discount",
                                      comment="活动类型:discount或voucher")
    enabled = Column(Integer, default=1,comment="是否启用:0-否,1-是")
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

class ShopeeTaskResult(Base):
    __tablename__ = "shopee_task_result"
    id = Column(Integer, primary_key=True, index=True)
    e_commerce_activity_id = Column(BIGINT)  #   活动ID
    e_commerce_activity_type = Column(String(100),nullable=False,default="discount",comment="活动类型:discount或voucher")
    # user_id = Column(Integer, ForeignKey("user.id"))
    task_result = Column(Text, nullable=True)
    task_name = Column(String(200), nullable=True)
    # notify_success = Column(Integer, default=1, nullable=True,comment="是否通知成功,1-成功，0-不成功")
    shop_id = Column(Integer, nullable=False)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))



class ShopeeVoucherMonitorConfig(Base):
    __tablename__ = "shopee_voucher_monitor_configs"
    id = Column(Integer, primary_key=True, index=True)
    voucher_id = Column(BIGINT)
    voucher_name = Column(String(200))
    shop_id = Column(Integer)  # 店铺ID
    interval_seconds = Column(Integer)  # 执行间隔（秒）
    next_run_time = Column(DateTime)  # 下次执行时间
    enabled = Column(Integer, default=1, comment="是否启用:0-否,1-是")
    user_subscription = Column(Integer, default=0, comment="用户订阅:0-否,1-是")
    activity_end_time = Column(DateTime)
    activity_start_time = Column(DateTime)
    check_rule = Column(Text, nullable=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc),
                         onupdate=lambda: datetime.now(timezone.utc))
    __table_args__ = (
        # 联合唯一索引
        UniqueConstraint('shop_id', 'voucher_id', name='uq_cfg_shopid_voucherid'),
    )

class ShopeeVoucherMonitorResult(Base):
    __tablename__ = "shopee_voucher_monitor_result"
    id = Column(Integer, primary_key=True, index=True)
    voucher_id = Column(BIGINT)
    voucher_name = Column(String(200))
    shop_id = Column(Integer)  # 店铺ID，移除unique约束
    voucher_code = Column(String(200))
    usage_quantity = Column(Integer)
    current_usage = Column(Integer)
    activity_start_time = Column(DateTime)
    activity_end_time = Column(DateTime)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc),
                         onupdate=lambda: datetime.now(timezone.utc))
    
    __table_args__ = (
        # 联合唯一索引
        UniqueConstraint('shop_id', 'voucher_id', name='uq_result_shopid_voucherid'),
    )
class ShopeeItemMonitorResult(Base):
    __tablename__ = "shopee_item_monitor_result"
    id = Column(Integer, primary_key=True, index=True)
    shop_id = Column(Integer)
    shop_name = Column(String(200))
    item_name = Column(String(500))
    item_sku = Column(String(500))
    item_id = Column(BIGINT)
    model_id = Column(BIGINT)
    model_name = Column(String(500))
    model_sku = Column(String(500))
    original_price = Column(Float)
    total_available_stock = Column(Float)
    currency = Column(String(100))
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc),
                         onupdate=lambda: datetime.now(timezone.utc))

# tiktok
class TiktokAuthToken(Base):
    __tablename__ = 'tiktok_auth_token'
    user_id = Column(Integer, ForeignKey('user.id'))
    id = Column(Integer, primary_key=True, index=True)
    open_id = Column(Integer, nullable=True)
    access_token = Column(String(100), nullable=False)
    refresh_token = Column(String(100), nullable=True)
    expires_in = Column(BIGINT, nullable=True)
    refresh_expires_in = Column(BIGINT, nullable=True)
    token_type = Column(String(100), nullable=True)
    scope = Column(String(500), nullable=True)
    api_type = Column(String(100), nullable=True)
    advertiser_ids = Column(String(500), nullable=True)
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc),
                         onupdate=lambda: datetime.now(timezone.utc))




class ProductInventory(Base):
    __tablename__ = 'product_inventory'
    __table_args__ = {'comment': '商品库存表'}

    # 商品编码，主键，唯一标识商品
    product_code = Column(String(50), primary_key=True, comment='商品编码')
    # 图片，存储图片URL或路径
    image = Column(String(255), comment='图片')
    # 商品名称
    product_name = Column(String(100), comment='商品名称')
    # 商品类型
    product_type = Column(String(50), comment='商品类型')
    # SPU/款式名称
    spu_style_name = Column(String(100), comment='SPU/款式名称')
    # 系统商品状态，如上架、下架等
    system_product_status = Column(String(20), comment='系统商品状态')
    # 库存表现，描述库存健康状况
    inventory_performance = Column(String(50), comment='库存表现')
    # 低库存预警，是否触发低库存
    low_inventory_alert = Column(Boolean, comment='低库存预警')
    # 仓库商品状态
    warehouse_product_status = Column(String(20), comment='仓库商品状态')
    # 颜色
    color = Column(String(50), comment='颜色')
    # 尺码
    size = Column(String(50), comment='尺码')
    # 单品毛重，单位克
    single_item_gross_weight = Column(DECIMAL(10, 2), comment='单品毛重')
    # 单品净重，单位克
    single_item_net_weight = Column(DECIMAL(10, 2), comment='单品净重')
    # 单品尺寸，格式如"长x宽x高"
    single_item_dimensions = Column(String(50), comment='单品尺寸')
    # UPC，通用产品代码
    upc = Column(String(50), comment='UPC')
    # 分类
    category = Column(String(50), comment='分类')
    # 品牌
    brand = Column(String(50), comment='品牌')
    # 单位，如件、箱等
    unit = Column(String(20), comment='单位')
    # 自定义字段1
    custom_field_1 = Column(String(100), comment='自定义1')
    # 自定义字段2
    custom_field_2 = Column(String(100), comment='自定义2')
    # 开发员，负责开发的人员
    developer = Column(String(50), comment='开发员')
    # 销售员，负责销售的人员
    salesperson = Column(String(50), comment='销售员')
    # 采购单价
    purchase_unit_price = Column(String(100), comment='采购单价')
    # 默认售价
    default_sale_price = Column(String(100), comment='默认售价')
    # 批发价
    wholesale_price = Column(DECIMAL(10, 2), comment='批发价')
    # 币种，如CNY, USD
    currency = Column(String(10), comment='币种')
    # 仓库，存储仓库名称或ID
    warehouse = Column(String(50), comment='仓库')
    # 计划采购量
    planned_purchase_quantity = Column(Integer, comment='计划采购量')
    # 采购在途量
    purchase_in_transit = Column(Integer, comment='采购在途')
    # 头程在途量
    first_leg_in_transit = Column(Integer, comment='头程在途')
    # 调拨在途量
    transfer_in_transit = Column(Integer, comment='调拨在途')
    # 加工在途量
    processing_in_transit = Column(Integer, comment='加工在途')
    # 退货在途量
    return_in_transit = Column(Integer, comment='退货在途')
    # 手工在途量
    manual_in_transit = Column(Integer, comment='手工在途')
    # 总在途量
    total_in_transit = Column(Integer, comment='在途量')
    # 可用量
    available_quantity = Column(Integer, comment='可用量')
    # 订单占用量
    order_occupied = Column(Integer, comment='订单占用')
    # 头程占用量
    first_leg_occupied = Column(Integer, comment='头程占用')
    # 调拨占用量
    transfer_occupied = Column(Integer, comment='调拨占用')
    # 加工占用量
    processing_occupied = Column(Integer, comment='加工占用')
    # 占用量
    total_occupied = Column(Integer, comment='占用量')
    # 不良品量
    defective_quantity = Column(Integer, comment='不良品量')
    # 在库量
    on_hand_quantity = Column(Integer, comment='在库量')
    # 采购成本
    purchase_cost = Column(String(100), comment='采购成本')
    # 可用成本
    available_cost = Column(DECIMAL(12, 2), comment='可用成本')
    # 占用成本
    occupied_cost = Column(DECIMAL(12, 2), comment='占用成本')
    # 在库成本
    on_hand_cost = Column(DECIMAL(12, 2), comment='在库成本')
    # 不良品成本
    defective_cost = Column(DECIMAL(12, 2), comment='不良品成本')
    # 可用货值
    available_value = Column(DECIMAL(12, 2), comment='可用货值')
    # 占用货值
    occupied_value = Column(DECIMAL(12, 2), comment='占用货值')
    # 在库货值
    on_hand_value = Column(DECIMAL(12, 2), comment='在库货值')
    # 不良品货值
    defective_value = Column(DECIMAL(12, 2), comment='不良品货值')
    # 3天销量
    sales_3_days = Column(Integer, comment='3天销量')
    sales_7_days = Column(Integer, comment='7天销量')
    sales_15_days = Column(Integer, comment='15天销量')
    sales_30_days = Column(Integer, comment='30天销量')
    sales_60_days = Column(Integer, comment='60天销量')
    sales_90_days = Column(Integer, comment='90天销量')
    daily_avg_sales_3_days = Column(DECIMAL(10, 2), comment='3天日均')
    daily_avg_sales_7_days = Column(DECIMAL(10, 2), comment='7天日均')
    daily_avg_sales_15_days = Column(DECIMAL(10, 2), comment='15天日均')
    daily_avg_sales_30_days = Column(DECIMAL(10, 2), comment='30天日均')
    daily_avg_sales_60_days = Column(DECIMAL(10, 2), comment='60天日均')
    daily_avg_sales_90_days = Column(DECIMAL(10, 2), comment='90天日均')
    custom_daily_avg = Column(DECIMAL(10, 2), comment='自定义日均')
    sellable_days = Column(Integer, comment='可售天数')
    out_of_stock_date = Column(DateTime, comment='缺货日期')
    purchase_in_transit_cost = Column(DECIMAL(12, 2), comment='采购在途成本')
    first_leg_in_transit_cost = Column(DECIMAL(12, 2), comment='头程在途成本')
    transfer_in_transit_cost = Column(DECIMAL(12, 2), comment='调拨在途成本')
    processing_in_transit_cost = Column(DECIMAL(12, 2), comment='加工在途成本')
    return_in_transit_cost = Column(DECIMAL(12, 2), comment='退货在途成本')
    total_in_transit_cost = Column(DECIMAL(12, 2), comment='在途成本')
    purchase_in_transit_value = Column(DECIMAL(12, 2), comment='采购在途货值')
    first_leg_in_transit_value = Column(DECIMAL(12, 2), comment='头程在途货值')
    transfer_in_transit_value = Column(DECIMAL(12, 2), comment='调拨在途货值')
    processing_in_transit_value = Column(DECIMAL(12, 2), comment='加工在途货值')
    return_in_transit_value = Column(DECIMAL(12, 2), comment='退货在途货值')
    total_in_transit_value = Column(DECIMAL(12, 2), comment='在途货值')
    fba_sellable = Column(Integer, comment='FBA可售')
    planned_in_transit_quantity = Column(Integer, comment='计划在途量')
    pending_order_occupied_quantity = Column(Integer, comment='待审核订单预占量')
    estimated_available_quantity = Column(Integer, comment='预计可用量')
    recommended_replenishment_quantity = Column(Integer, comment='推荐补货数')
    initial_inventory_age = Column(Integer, comment='首批库龄')
    estimated_turnover_days = Column(Integer, comment='预计周转天数')
    # 记录创建时间，UTC时间
    created_at = Column(DateTime, server_default=func.utc_timestamp(), comment='记录创建时间')
    # 记录更新时间，UTC时间
    updated_at = Column(DateTime, server_default=func.utc_timestamp(), onupdate=func.utc_timestamp(), comment='记录更新时间')

class ShopeeShopwarehouse(Base):
    __tablename__ = 'shopee_shop_warehouse'
    __table_args__ =  {'comment': 'Shopee店铺和仓库映射关系'}

    id = Column(Integer, primary_key=True, index=True)
    shop_id = Column(Integer, comment='店铺ID')
    warehouse_id = Column(Integer, comment='仓库ID')
    warehouse_name = Column(String(100), comment='仓库名称')
    warehouse_code = Column(String(100), comment='仓库编码')
    warehouse_type = Column(String(100), comment='仓库类型')
    warehouse_status = Column(String(100), comment='仓库状态')
    warehouse_address = Column(String(100), comment='仓库地址')
    create_time = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc),
                         onupdate=lambda: datetime.now(timezone.utc))

# class ShopeeShopItemWithIssues(Base):
#     __tablename__ = 'shopee_shop_item_with_issues'
#     id = Column(Integer, primary_key=True, index=True)
#     shop_id = Column(Integer, comment='店铺ID')
#     shop_name = Column(String(100), comment='店铺名称')
#     item_id = Column(Integer, comment='商品ID')
#     item_name = Column(String(100), comment='商品名称')
#     model_id = Column(Integer, comment='规格ID')
#     model_name = Column(String(100), comment='规格名称')
#     reason = Column(String(100), comment='问题类型')
#     reason_description = Column(String(100), comment='问题描述')
#     # 记录创建时间，UTC时间
#     created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
#     # 记录更新时间，UTC时间
#     updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc),
#                          onupdate=lambda: datetime.now(timezone.utc))


class ShopeeAccountHealthCache(Base):
    """Shopee账户健康缓存主表"""
    __tablename__ = 'shopee_account_health_cache'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    shop_id = Column(BigInteger, nullable=False, unique=True, comment='Shopee店铺ID')
    shop_name = Column(String(255), comment='店铺名称')

    # 处罚信息汇总
    overall_penalty_points = Column(Integer, default=0, comment='总处罚分')
    non_fulfillment_rate_points = Column(Integer, default=0, comment='未履约处罚分')
    late_shipment_rate_points = Column(Integer, default=0, comment='延迟发货处罚分')
    listing_violations_points = Column(Integer, default=0, comment='商品违规处罚分')
    opfr_violations_points = Column(Integer, default=0, comment='OPFR违规处罚分')
    others_points = Column(Integer, default=0, comment='其他处罚分')
    has_ongoing_punishment = Column(Boolean, default=False, comment='是否有正在进行的处罚')
    ongoing_punishment_count = Column(Integer, default=0, comment='正在进行的处罚数量')

    # 表现概览汇总
    overall_rating = Column(Integer, comment='整体评级')
    overall_rating_description = Column(String(50), comment='整体评级描述')
    fulfillment_failed = Column(Integer, default=0, comment='履约失败指标数')
    listing_failed = Column(Integer, default=0, comment='商品失败指标数')
    custom_service_failed = Column(Integer, default=0, comment='客服失败指标数')
    metric_count = Column(Integer, default=0, comment='指标总数')
    performance_last_updated = Column(DateTime(timezone=True), comment='表现数据最后更新时间')

    # 处罚详情统计
    penalty_point_history_count = Column(Integer, default=0, comment='处罚分历史记录数')
    punishment_history_count = Column(Integer, default=0, comment='处罚历史记录数')
    listings_with_issues_count = Column(Integer, default=0, comment='问题商品数')
    late_orders_count = Column(Integer, default=0, comment='延迟订单数')

    # 系统字段
    create_time = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc),
                         comment='创建时间')
    update_time = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc),
                         onupdate=lambda: datetime.now(timezone.utc), comment='更新时间')


class ShopeeOngoingPunishmentCache(Base):
    """Shopee正在进行的处罚缓存表"""
    __tablename__ = 'shopee_ongoing_punishment_cache'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    shop_id = Column(BigInteger, nullable=False, index=True, comment='Shopee店铺ID')
    shop__name = Column(String(500), comment='店铺名称')
    punishment_name = Column(String(100), comment='处罚名称')
    punishment_tier = Column(Integer, comment='处罚等级(1-5)')
    days_left = Column(Integer, comment='剩余天数')
    punishment_type = Column(String(100), comment='处罚类型(兼容旧字段)')
    punishment_id = Column(String(100), comment='处罚ID(兼容旧字段)')
    start_time = Column(DateTime(timezone=True), comment='开始时间(兼容旧字段)')
    end_time = Column(DateTime(timezone=True), index=True, comment='结束时间(兼容旧字段)')
    reason = Column(String(500), comment='处罚原因(兼容旧字段)')
    severity = Column(String(50), comment='严重程度(兼容旧字段)')
    description = Column(Text, comment='处罚描述(兼容旧字段)')

    # 系统字段
    create_time = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc),
                         comment='创建时间')
    update_time = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc),
                         onupdate=lambda: datetime.now(timezone.utc), comment='更新时间')


class ShopeePenaltyPointHistoryCache(Base):
    """Shopee处罚分历史缓存表"""
    __tablename__ = 'shopee_penalty_point_history_cache'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    shop_id = Column(BigInteger, nullable=False, index=True, comment='Shopee店铺ID')
    shop__name = Column(String(500), comment='店铺名称')
    issue_time = Column(DateTime(timezone=True), index=True, comment='处罚分发放时间')
    latest_point_num = Column(Integer, default=0, comment='最新处罚分数')
    original_point_num = Column(Integer, default=0, comment='原始处罚分数')
    reference_id = Column(BigInteger, index=True, comment='参考ID')
    violation_type = Column(Integer, comment='违规类型')
    violation_type_description = Column(String(200), comment='违规类型描述')

    # 系统字段
    create_time = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc),
                         comment='创建时间')
    update_time = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc),
                         onupdate=lambda: datetime.now(timezone.utc), comment='更新时间')


class ShopeePunishmentHistoryCache(Base):
    """Shopee处罚历史缓存表"""
    __tablename__ = 'shopee_punishment_history_cache'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    shop_id = Column(BigInteger, nullable=False, index=True, comment='Shopee店铺ID')
    shop__name = Column(String(500), comment='店铺名称')
    issue_time = Column(DateTime(timezone=True), index=True, comment='处罚发放时间')
    start_time = Column(DateTime(timezone=True), comment='处罚开始时间')
    end_time = Column(DateTime(timezone=True), comment='处罚结束时间')
    punishment_type = Column(Integer, comment='处罚类型')
    punishment_type_description = Column(String(200), comment='处罚类型描述')
    reason = Column(Integer, comment='处罚原因')
    reason_description = Column(String(200), comment='处罚原因描述')
    reference_id = Column(BigInteger, index=True, comment='参考ID')
    listing_limit = Column(Integer, comment='商品限制数量')
    order_limit = Column(String(50), comment='订单限制百分比')

    # 系统字段
    create_time = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc),
                         comment='创建时间')
    update_time = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc),
                         onupdate=lambda: datetime.now(timezone.utc), comment='更新时间')


class ShopeeListingsIssuesCache(Base):
    """Shopee问题商品缓存表"""
    __tablename__ = 'shopee_listings_issues_cache'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    shop_id = Column(BigInteger, nullable=False, index=True, comment='Shopee店铺ID')
    shop__name = Column(String(500), comment='店铺名称')
    item_id = Column(BigInteger, nullable=False, index=True, comment='商品ID')
    item_name = Column(String(500), comment='商品名称')
    model_id = Column(BigInteger, comment='型号ID')
    model_name = Column(String(200), comment='型号名称')
    detailed_reason = Column(Integer, comment='问题详细原因')
    detailed_reason_description = Column(String(200), comment='问题详细原因描述')
    update_time = Column(DateTime(timezone=True), index=True, comment='更新时间')

    # 系统字段
    cache_create_time = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc),
                         comment='创建时间')
    cache_update_time = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc),
                               onupdate=lambda: datetime.now(timezone.utc), comment='缓存更新时间')


class ShopeeLateOrdersCache(Base):
    """Shopee延迟订单缓存表"""
    __tablename__ = 'shopee_late_orders_cache'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    shop_id = Column(BigInteger, nullable=False, index=True, comment='Shopee店铺ID')
    shop__name = Column(String(500), comment='店铺名称')
    order_sn = Column(String(100), nullable=False, index=True, comment='订单编号')
    shipping_deadline = Column(DateTime(timezone=True), index=True, comment='发货截止时间')
    actual_shipping_time = Column(DateTime(timezone=True), comment='实际发货时间')
    late_by_days = Column(Integer, default=0, comment='延迟天数')

    # 系统字段
    create_time = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc),
                         comment='创建时间')
    update_time = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc),
                         onupdate=lambda: datetime.now(timezone.utc), comment='更新时间')


class ShopeeMetricsCache(Base):
    """Shopee指标详情缓存表"""
    __tablename__ = 'shopee_metrics_cache'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    shop_id = Column(BigInteger, nullable=False, index=True, comment='Shopee店铺ID')
    metric_id = Column(Integer, nullable=False, index=True, comment='指标ID')
    metric_name = Column(String(200), comment='指标名称')
    score = Column(Integer, default=0, comment='指标分数')
    target_value = Column(Float, comment='目标值')
    target_comparator = Column(String(10), comment='目标比较符(<=, >=, <, >, =)')
    target_json = Column(Text, comment='完整目标信息JSON')
    status = Column(String(50), comment='指标状态')
    detail = Column(Text, comment='指标详情JSON数据')

    # 系统字段
    create_time = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc), comment='创建时间')
    update_time = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), comment='更新时间')