import json
import uuid

import lark_oapi as lark
from lark_oapi.api.im.v1 import *

# SDK 使用说明: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/server-side-sdk/python--sdk/preparations-before-development
# 以下示例代码默认根据文档示例值填充，如果存在代码问题，请在 API 调试台填上相关必要参数后再复制代码使用
# 复制该 Demo 后, 需要将 "YOUR_APP_ID", "YOUR_APP_SECRET" 替换为自己应用的 APP_ID, APP_SECRET.

# open_id和用户直接相关.以下receive_id填写open_id(如果消息接收者为用户，推荐使用用户的 open_id。)
open_id = 'ou_6bb47bd9d16a2750b73a38e3579988a2'
app_id = 'cli_a8b90a54c13ad00e'
app_secret = "M89dWcoaphwwVMROQnZuydkauMK6gqMp"
l_id = "ou_16a1debeea4385000b506eb07a0a3813"
qun_id = 'oc_a996df5aa6e0008b8e5526f750bd32ef'

# SDK 使用说明: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/server-side-sdk/python--sdk/preparations-before-development
# 以下示例代码默认根据文档示例值填充，如果存在代码问题，请在 API 调试台填上相关必要参数后再复制代码使用
# 复制该 Demo 后, 需要将 "YOUR_APP_ID", "YOUR_APP_SECRET" 替换为自己应用的 APP_ID, APP_SECRET.
def send_message(receive_id_type,receive_id,msg_type):
    # 创建client
    client = lark.Client.builder() \
        .app_id(app_id) \
        .app_secret(app_secret) \
        .log_level(lark.LogLevel.DEBUG) \
        .build()

    # 构造请求对象
    content = {"config":{"wide_screen_mode":True},"elements":[{"alt":{"content":"","tag":"plain_text"},"img_key":"img_7ea74629-9191-4176-998c-2e603c9c5e8g","tag":"img"},{"tag":"div","text":{"content":"你的折扣活动(瘦身爆款系列女装618大促)临近过期\\\n你的优惠券活动(618店庆)临近过期\\\n\\\n你的折扣活动(海蓝系列)有商品无法开启\\\n\\\n📬 <at user_id=\"ou_6bb47bd9d16a2750b73a38e3579988a2\">李有星</at>","tag":"lark_md"}},{"actions":[{"tag":"button","text":{"content":"查看活动详情","tag":"plain_text"},"type":"default","url":"https://dashboard.aiulink.com/"}],"tag":"action"}],"header":{"template":"turquoise","title":{"content":"📚折扣活动提醒","tag":"plain_text"}}}

    request: CreateMessageRequest = CreateMessageRequest.builder() \
        .receive_id_type(receive_id_type) \
        .request_body(CreateMessageRequestBody.builder()
            .receive_id(receive_id)
            .msg_type(msg_type)
            .content(json.dumps(content,ensure_ascii=False))
            .uuid( str(uuid.uuid4()).replace('-', ''))
            .build()) \
        .build()

    # 发起请求
    response: CreateMessageResponse = client.im.v1.message.create(request)

    # 处理失败返回
    if not response.success():
        lark.logger.error(
            f"client.im.v1.message.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
        return

    # 处理业务结果
    lark.logger.info(lark.JSON.marshal(response.data, indent=4))


if __name__ == "__main__":
    receive_id_type = 'chat_id'
    receive_id = qun_id
    send_message(receive_id_type,receive_id,'interactive')

    # receive_id_type = 'open_id'
    # receive_id = open_id
    # send_message(receive_id_type,receive_id,'interactive')
