import hmac
import json
import time
import requests
import hashlib
from config.settings import settings

def shop_auth():
    timest = int(time.time())
    host = settings.SHOPEE_HOST
    path = "/api/v2/shop/auth_partner"
    redirect_url = "https://api.aiulink.com/api/shopee_auth_callback"
    # redirect_url = "http://localhost:9000/api/shopee_auth_callback"
    partner_id = settings. SHOPEE_PARTNER_ID
    partner_app_key = settings.SHOPEE_PARTNER_KEY
    partner_key = partner_app_key.encode()
    tmp_base_string = "%s%s%s" % (partner_id, path, timest)
    base_string = tmp_base_string.encode()
    sign = hmac.new(partner_key, base_string, hashlib.sha256).hexdigest()
    ##generate api
    url = host + path + "?partner_id=%s&timestamp=%s&sign=%s&redirect=%s" % (partner_id, timest, sign, redirect_url)
    print(url)


if  __name__ == '__main__':
    shop_auth()