from datetime import datetime, timezone

import requests
import json
import time
import hmac
import hashlib

from app.model.models import SHopeeAuthToken, ShopeeShopUsers, ShopeeVoucherMonitorResult, ShopeeVoucherMonitorConfig
from app.scheduler_tasks.shopee_activity_monitor import get_shopee_one_shop_discount_items
from app.scheduler_tasks.shopee_shop_no_activity_items import get_shopee_shop_active_items
from app.scheduler_tasks.shopee_voucher import  save_voucher_infos
from app.utils.shopee_utils import get_access_token_shop_level, shopee_sign
from config.settings import settings
from sqlalchemy.orm import Session
from app.database.database import get_db, init_db, SessionLocal
from fastapi import APIRouter, HTTPException, Depends
from app.model import models

db = next(get_db())
# 配置参数
partner_id = settings.SHOPEE_PARTNER_ID
secret_key = settings.SHOPEE_PARTNER_KEY


def get_shope_discount():
    shop_id = 1506671934  # 替换为你的店铺 ID
    shopee_current_token = db.query(models.SHopeeAuthToken).filter(
        models.SHopeeAuthToken.shop_id == shop_id).first()
    if not shopee_current_token:
        raise HTTPException(status_code=404, detail=f"{shop_id} miss authorization")

    access_token, timestamp = get_access_token_shop_level(db, shop_id, partner_id, settings.SHOPEE_PARTNER_KEY,
                                                          shopee_current_token)
    api_path = "/api/v2/discount/get_discount_list"
    sign, timestamp = shopee_sign(partner_id, api_path, settings.SHOPEE_PARTNER_KEY, access_token, shop_id)
    url = settings.SHOPEE_HOST + api_path + "?partner_id=%s&timestamp=%s&access_token=%s&sign=%s&shop_id=%s" % (
        partner_id,
        timestamp,
        access_token,
        sign,
        shop_id)
    # 构造请求头
    headers = {"Content-Type": "application/json"}
    params = {
        "discount_status": "all",
        "page_no": 1,
        "page_size": 100
    }
    resp = requests.get(url, params=params, headers=headers)
    # print(resp.json())
    response = resp.json().get("response")
    discount_list = response.get("discount_list")
    # for discount in discount_list:
    #     discount_id = discount.get("discount_id")
    #     key = (shop_id, discount_id)
    #     # print(f"***********{key}***********")
    # print(discount_list)
    obj = []
    item_list = []
    for discount in discount_list:
        # 获取折扣详情
        discount_id = discount.get("discount_id")
        # if discount_id!=565901846118400:
        #     continue
        api_path = "/api/v2/discount/get_discount"
        sign, timestamp = shopee_sign(partner_id, api_path, settings.SHOPEE_PARTNER_KEY, access_token, shop_id)
        url = settings.SHOPEE_HOST + api_path + "?partner_id=%s&timestamp=%s&access_token=%s&sign=%s&shop_id=%s" % (
            partner_id, timestamp, access_token, sign, shop_id)
        params = {
            "discount_id": discount_id,
            "page_no": 1,
            "page_size": 100
        }
        resp = requests.get(url, params=params, headers=headers)
        obj.append(resp.json().get("response"))
        response = resp.json().get("response")
        if not response:
            print('-----------------')
            print(params)
            print('-----------------')
        item_list = item_list + resp.json().get("response").get("item_list")
    print(json.dumps(obj, indent=4, ensure_ascii=False))

    # 获取商品详情
    # model_stock = []
    # for item in item_list:
    #     item_id = item.get("item_id")
    #     model_list = item.get("model_list", None)
    #     if model_list:
    #
    #         api_path = "/api/v2/product/get_model_list"
    #         sign, timestamp = shopee_sign(partner_id, api_path, settings.SHOPEE_PARTNER_KEY, access_token, shop_id)
    #         url = settings.SHOPEE_HOST + api_path + "?partner_id=%s&timestamp=%s&access_token=%s&sign=%s&shop_id=%s" % (
    #             partner_id, timestamp, access_token, sign, shop_id
    #         )
    #         params = {
    #             "item_id": item_id,
    #         }
    #         resp = requests.get(url, params=params, headers=headers)
    #         response = resp.json().get("response")
    #         model_list = response.get("model")
    #         for model in model_list:
    #             model_id = model.get("model_id")
    #             model_name = model.get("model_name")
    #             promotion_id = model.get("promotion_id")
    #             model_sku = model.get("model_sku")
    #             stock_info_v2 = model.get("stock_info_v2")
    #             model_stock.append(
    #                 {
    #                     "item_id": item_id,
    #                     "model_id": model_id,
    #                     "model_name": model_name,
    #                     "promotion_id": promotion_id,
    #                     "model_sku": model_sku,
    #                     "stock_info_v2": stock_info_v2
    #                 }
    #             )
    # # print(json.dumps(model_stock, indent=4, ensure_ascii=False))
    #

def get_shop_info():
    shop_id_list = [1313851755,
                    1313852439,
                    848525214,
                    848738488,
                    848739804,
                    848740244,
                    848740789,
                    854406887,
                    1202331251,
                    1272297352,
                    1272297356,
                    1412114267,
                    1506671934,
                    1515534386,
                    1520662175]
    result = []
    for shop_id in shop_id_list:
        shopee_current_token = db.query(models.SHopeeAuthToken).filter(
            models.SHopeeAuthToken.shop_id == shop_id).first()
        if not shopee_current_token:
            raise HTTPException(status_code=404, detail=f"{shop_id} miss authorization")

        access_token, timestamp = get_access_token_shop_level(db, shop_id, partner_id, settings.SHOPEE_PARTNER_KEY,
                                                              shopee_current_token)
        api_path = "/api/v2/shop/get_shop_info"
        sign, timestamp = shopee_sign(partner_id, api_path, settings.SHOPEE_PARTNER_KEY, access_token, shop_id)
        url = settings.SHOPEE_HOST + api_path + "?partner_id=%s&timestamp=%s&access_token=%s&sign=%s&shop_id=%s" % (
            partner_id,
            timestamp,
            access_token,
            sign,
            shop_id)
        # 构造请求头
        headers = {"Content-Type": "application/json"}
        # params = {
        #     "discount_status": "ongoing",
        #     "page_no": 1,
        #     "page_size": 100
        # }
        resp = requests.get(url,  headers=headers)
        response = resp.json()
        shop_name=response.get("shop_name")
        print(f'---------------{shop_id} ,response={shop_name}')
        # return response
    #     result.append(response)
    # return result

def get_item_info():
    item_id = 27906098722
    api_path = "/api/v2/product/get_item_base_info"
    shop_id = 1313851755
    access_token = 'eyJhbGciOiJIUzI1NiJ9.CN_iehABGOuSv_IEIAEo-qSgwgYwg-qBfzgBQAE.RlhAuKc30_U870YCy6Wpd8yhStLAyLOm6_Yalxj2q9A'
    sign, timestamp = shopee_sign(settings.SHOPEE_PARTNER_ID, api_path, settings.SHOPEE_PARTNER_KEY,
                                  access_token, shop_id)
    url = settings.SHOPEE_HOST + api_path + "?partner_id=%s&timestamp=%s&access_token=%s&sign=%s&shop_id=%s" % (
        settings.SHOPEE_PARTNER_ID, timestamp, access_token, sign, shop_id
    )
    get_item_base_info_params = {
        "item_id_list": [item_id]
    }
    item_info_result = requests.get(url, params=get_item_base_info_params).json().get(
        "response")
    item_list = item_info_result.get("item_list")
    print(json.dumps(item_list, indent=4, ensure_ascii=False))

def get_shop_active_items_for_discount():
    shops = db.query(models.SHopeeAuthToken).filter(SHopeeAuthToken.shop_id ==1515534386).all()
    for shop in shops:
        shop_id = shop.shop_id
        current_token = db.query(models.SHopeeAuthToken).filter(
            models.SHopeeAuthToken.shop_id == shop_id).first()
        # {(item_id, model_id):( item_name, model_name)}
        start_time = datetime.now()
        result_dict = get_shopee_shop_active_items(current_token, db)
        end_time = datetime.now()
        print(f"获取店{shop_id}铺折扣活动信息耗时: {(end_time - start_time).total_seconds()}秒")

def get_shopee_shop_no_discount_items():
    shop_id = 1506671934
    # 构建基础查询
    shop_tokens = db.query(SHopeeAuthToken).filter(
        SHopeeAuthToken.shop_id == shop_id
    ).first()
    # set(item_id, model_id,item_name,model_name)
    discount_list_result,item_model_discount_info_map = get_shopee_one_shop_discount_items(shop_tokens, db)
    shop_active_items = get_shopee_shop_active_items(shop_tokens, db)
    shop_active_item_model_no_price_set = set(shop_active_items.keys())
    shop_items_not_in_discount = shop_active_item_model_no_price_set - discount_list_result

    data={
        "items": [
            {
                "item_id": item[0],
                "model_id": item[1],
                "item_name": item[2],
                "model_name": item[3]
            }
            for item in shop_items_not_in_discount
        ]
    }
    print(data)
    pass




if __name__ == '__main__':
    # get_item_info()
    # get_shop_info()
    # for i in range(10):
    # result = get_shope_discount()
        # time.sleep(10)
    # print(json.dumps(result, indent=4, ensure_ascii=False))
    # get_all_shop_activities()
    get_shop_active_items_for_discount()
    # get_shopee_shop_no_discount_items()

    # generate_shopee_voucher_info()