#!/usr/bin/env python3
"""
Shopee API 性能测试脚本
比较优化前后的性能差异
"""

import time
import asyncio
import logging
from datetime import datetime
from sqlalchemy.orm import Session

from app.scheduler_tasks.shopee_shop_no_activity_items import get_shopee_shop_active_items
from app.model.models import SHopeeAuthToken
from app.database.database import get_db

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_performance():
    """测试性能"""
    db = next(get_db())
    
    # 获取一个测试用的token
    test_tokens = db.query(SHopeeAuthToken).all()
    # if not test_token:
    #     logger.error("没有找到测试用的Shopee认证token")
    #     return
    for test_token in test_tokens:

        logger.info(f"开始性能测试，使用shop_id: {test_token.shop_id}")

        # 测试优化后的函数
        start_time = time.time()
        try:
            result = get_shopee_shop_active_items(test_token, db)
            end_time = time.time()

            execution_time = end_time - start_time
            item_count = len(result)

            logger.info(f"性能测试结果:")
            logger.info(f"执行时间: {execution_time:.2f} 秒")
            logger.info(f"获取商品数量: {item_count}")
            logger.info(f"平均每个商品耗时: {execution_time/max(item_count, 1):.4f} 秒")

            # 记录到文件
            with open("performance_log.txt", "a", encoding="utf-8") as f:
                f.write(f"{datetime.now()}: 执行时间={execution_time:.2f}s, 商品数量={item_count}\n")

        except Exception as e:
            logger.error(f"性能测试失败: {e}")
    
    db.close()


async def test_async_performance():
    """测试异步版本性能"""
    from app.scheduler_tasks.shopee_shop_no_activity_items import get_shopee_shop_active_items_async
    
    db = next(get_db())
    
    # 获取一个测试用的token
    test_tokens = db.query(SHopeeAuthToken).all()
    # if not test_token:
    #     logger.error("没有找到测试用的Shopee认证token")
    #     return
    for test_token in test_tokens:

        logger.info(f"开始异步性能测试，使用shop_id: {test_token.shop_id}")

        # 测试异步函数
        start_time = time.time()
        try:
            result = await get_shopee_shop_active_items_async(test_token, db)
            end_time = time.time()

            execution_time = end_time - start_time
            item_count = len(result)

            logger.info(f"异步性能测试结果:")
            logger.info(f"执行时间: {execution_time:.2f} 秒")
            logger.info(f"获取商品数量: {item_count}")
            logger.info(f"平均每个商品耗时: {execution_time/max(item_count, 1):.4f} 秒")

            # 记录到文件
            with open("async_performance_log.txt", "a", encoding="utf-8") as f:
                f.write(f"{datetime.now()}: 异步执行时间={execution_time:.2f}s, 商品数量={item_count}\n")

        except Exception as e:
            logger.error(f"异步性能测试失败: {e}")

    db.close()


def compare_performance():
    """比较同步和异步版本性能"""
    logger.info("开始性能对比测试...")
    
    # 测试同步版本
    logger.info("测试同步版本...")
    sync_start = time.time()
    test_performance()
    sync_time = time.time() - sync_start
    
    # 测试异步版本
    logger.info("测试异步版本...")
    async_start = time.time()
    asyncio.run(test_async_performance())
    async_time = time.time() - async_start
    
    logger.info(f"性能对比结果:")
    logger.info(f"同步版本总耗时: {sync_time:.2f} 秒")
    logger.info(f"异步版本总耗时: {async_time:.2f} 秒")
    logger.info(f"性能提升: {((sync_time - async_time) / sync_time * 100):.1f}%")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1]
        if test_type == "sync":
            test_performance()
        elif test_type == "async":
            asyncio.run(test_async_performance())
        elif test_type == "compare":
            compare_performance()
        else:
            logger.error("无效的测试类型，请使用: sync, async, compare")
    else:
        # 默认运行对比测试
        compare_performance() 