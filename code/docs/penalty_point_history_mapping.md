# 处罚分历史数据映射转换文档

## 📋 概述

`get_penalty_point_history`接口根据Shopee API文档返回店铺的处罚分历史记录。我们的系统会自动将返回数据中的数值型枚举字段转换为对应的描述字符串，并修正时间字段名称，以提高数据的可读性。

## 🔄 主要修正内容

### 1. 时间字段名称修正

**修正前**: `create_time`, `update_time`  
**修正后**: `issue_time`

根据API文档，处罚分历史记录中的时间字段应为`issue_time`（处罚分发放时间）。

### 2. 违规类型映射转换

**字段**: `violation_type`  
**适用列表**: `penalty_point_list`

## 🗂️ 违规类型完整映射表

### 基础违规类型 (5-25)

| 数值 | 描述字符串 | 说明 |
| --- | --- | --- |
| 5 | High Late Shipment Rate | 延迟发货率过高 |
| 6 | High Non-fulfilment Rate | 未履约率过高 |
| 7 | High number of non-fulfilled orders | 未履约订单数量过高 |
| 8 | High number of late shipped orders | 延迟发货订单数量过高 |
| 9 | Prohibited Listings | 禁售商品 |
| 10 | Counterfeit / IP infringement | 假冒/知识产权侵权 |
| 11 | Spam | 垃圾信息 |
| 12 | Copy/Steal images | 复制/盗用图片 |
| 13 | Re-uploading deleted listings with no change | 重新上传已删除商品且无变更 |
| 14 | Bought counterfeit from mall | 从商城购买假货 |
| 15 | Counterfeit caught by Shopee | Shopee发现的假货 |
| 16 | High percentage of pre-order listings | 预售商品比例过高 |
| 17 | Confirmed Fraud attempts (total) | 确认的欺诈尝试（总计） |
| 18 | Confirmed Fraud attempts per week (All with vouchers only) | 每周确认的欺诈尝试（仅限优惠券） |
| 19 | Fake return address | 虚假退货地址 |
| 20 | Shipping fraud/abuse | 运输欺诈/滥用 |
| 21 | High No. of Non-responded Chat | 未回复聊天数量过高 |
| 22 | Rude chat replies | 粗鲁的聊天回复 |
| 23 | Request buyer to cancel order | 要求买家取消订单 |
| 24 | Rude reply to buyer's review | 对买家评价的粗鲁回复 |
| 25 | Violate Return/Refund policy | 违反退换货政策 |

### 特殊违规类型 (101)

| 数值 | 描述字符串  | 说明     |
| ---- | ----------- | -------- |
| 101  | Tier Reason | 等级原因 |

### 高级违规类型 (3000+)

| 数值 | 描述字符串 | 说明 |
| --- | --- | --- |
| 3026 | Misuse of Shopee's IP | 滥用Shopee知识产权 |
| 3028 | Violate Shop Name Regulations | 违反店铺名称规定 |
| 3030 | Direct transactions outside of the Shopee platform | 在Shopee平台外直接交易 |
| 3032 | Shipping empty / incomplete parcels | 发送空包裹/不完整包裹 |
| 3034 | Severe Violations on Shopee Feed | Shopee动态严重违规 |
| 3036 | Severe Violations on Shopee LIVE | Shopee直播严重违规 |
| 3038 | Misuse of Local Vendor Tag | 滥用本地供应商标签 |
| 3040 | Use of misleading shop tag in listing image | 在商品图片中使用误导性店铺标签 |
| 3042 | Counterfeit / IP Infringement test | 假冒/知识产权侵权测试 |
| 3044 | Repeat Offender - IP infringement and Counterfeit listings | 重复违规者 - 知识产权侵权和假冒商品 |
| 3046 | Violation of Live Animals Selling Policy | 违反活体动物销售政策 |
| 3048 | Chat Spam | 聊天垃圾信息 |
| 3050 | High Overseas Return Refunds Rate | 海外退换货率过高 |
| 3052 | Privacy breach in buyer's review reply | 在买家评价回复中泄露隐私 |
| 3054 | Order Brushing | 刷单 |
| 3056 | porn image | 色情图片 |
| 3058 | Incorrect Product Categories | 错误的商品分类 |
| 3060 | Extremely High Non-Fulfilment Rate | 极高的未履约率 |
| 3062 | Penalty of Affiliate Marketing Solution (AMS) Overdue Invoice Payment | 联盟营销解决方案(AMS)逾期发票付款处罚 |
| 3064 | Government-related listing | 政府相关商品 |
| 3066 | Listing invalid gifted items | 上架无效赠品 |
| 3068 | High non-fulfilment rate (Next Day Delivery Orders) | 次日达订单未履约率过高 |
| 3070 | High Late Shipment Rate (Next Day Delivery Orders) | 次日达订单延迟发货率过高 |
| 3072 | OPFR Violation Value | OPFR违规值 |
| 3074 | Direct transactions outside Shopee platform via chat | 通过聊天在Shopee平台外直接交易 |

### 分级违规类型 (3090-3101)

| 数值 | 描述字符串                              | 说明              |
| ---- | --------------------------------------- | ----------------- |
| 3090 | Prohibited Listings-Extreme Violations  | 禁售商品-极端违规 |
| 3091 | Prohibited Listings-High Violations     | 禁售商品-高度违规 |
| 3092 | Prohibited Listings-Mid Violations      | 禁售商品-中度违规 |
| 3093 | Prohibited Listings-Low Violations      | 禁售商品-轻度违规 |
| 3094 | Counterfeit Listings-Extreme Violations | 假冒商品-极端违规 |
| 3095 | Counterfeit Listings-High Violations    | 假冒商品-高度违规 |
| 3096 | Counterfeit Listings-Mid Violations     | 假冒商品-中度违规 |
| 3097 | Counterfeit Listings-Low Violations     | 假冒商品-轻度违规 |
| 3098 | Spam Listings-Extreme Violations        | 垃圾商品-极端违规 |
| 3099 | Spam Listings-High Violations           | 垃圾商品-高度违规 |
| 3100 | Spam Listings-Mid Violations            | 垃圾商品-中度违规 |
| 3101 | Spam Listings-Low Violations            | 垃圾商品-轻度违规 |

### 其他违规类型

| 数值 | 描述字符串                                  | 说明                   |
| ---- | ------------------------------------------- | ---------------------- |
| 3145 | Return/Refund Rate (Non-integrated Channel) | 退换货率（非集成渠道） |
| 4130 | Poor Product Quality                        | 商品质量差             |

## 📊 数据结构说明

### API返回的字段结构

```json
{
  "penalty_point_list": [
    {
      "issue_time": 1728552398, // 处罚分发放时间（时间戳）
      "latest_point_num": 0, // 最新处罚分数
      "original_point_num": 1, // 原始处罚分数
      "reference_id": 764539404640322244, // 参考ID
      "violation_type": 5 // 违规类型（数值）
    }
  ],
  "total_count": 8 // 总记录数
}
```

### 转换后的数据结构

```json
{
  "penalty_point_list": [
    {
      "issue_time": "2024-10-10T07:19:58Z", // 转换为UTC时间字符串
      "latest_point_num": 0,
      "original_point_num": 1,
      "reference_id": 764539404640322244,
      "violation_type": 5, // 保留原始数值
      "violation_type_description": "High Late Shipment Rate" // 添加描述字段
    }
  ],
  "total_count": 8
}
```

## 🔄 转换示例

### 转换前（Shopee API原始返回）:

```json
[
  {
    "penalty_point_list": [
      {
        "issue_time": 1728552398,
        "latest_point_num": 0,
        "original_point_num": 1,
        "reference_id": 764539404640322244,
        "violation_type": 5
      }
    ],
    "total_count": 1
  }
]
```

### 转换后（我们的接口返回）:

```json
[
  {
    "penalty_point_list": [
      {
        "issue_time": "2024-10-10T07:19:58Z",
        "latest_point_num": 0,
        "original_point_num": 1,
        "reference_id": 764539404640322244,
        "violation_type": 5,
        "violation_type_description": "High Late Shipment Rate"
      }
    ],
    "total_count": 1
  }
]
```

## 🎯 设计原则

1. **字段名称修正**: 根据API文档修正时间字段名称
2. **保留原始数值**: 原始的数值字段保持不变，便于程序处理
3. **添加描述字段**: 新增 `_description` 后缀的字段，提供人类可读的描述
4. **向后兼容**: 不影响现有的数据结构和字段
5. **错误处理**: 对于未知的数值，返回 `Unknown(数值)` 格式的描述
6. **分页支持**: 支持分页数据的批量转换

## 🔧 前端使用建议

### 1. 处罚记录展示

```jsx
function PenaltyPointRecord({ record }) {
  return (
    <div className="penalty-record">
      <div className="violation-type">
        <span className="type-badge">{record.violation_type_description}</span>
      </div>
      <div className="points">
        <span>原始分数: {record.original_point_num}</span>
        <span>最新分数: {record.latest_point_num}</span>
      </div>
      <div className="time">
        发放时间: {new Date(record.issue_time).toLocaleString()}
      </div>
      <div className="reference">参考ID: {record.reference_id}</div>
    </div>
  );
}
```

### 2. 违规类型分类展示

```javascript
// 按违规严重程度分类
function categorizeViolations(records) {
  const categories = {
    extreme: [], // 极端违规 (3090, 3094, 3098)
    high: [], // 高度违规 (3091, 3095, 3099)
    mid: [], // 中度违规 (3092, 3096, 3100)
    low: [], // 轻度违规 (3093, 3097, 3101)
    other: [], // 其他违规
  };

  records.forEach((record) => {
    const type = record.violation_type;
    if ([3090, 3094, 3098].includes(type)) {
      categories.extreme.push(record);
    } else if ([3091, 3095, 3099].includes(type)) {
      categories.high.push(record);
    } else if ([3092, 3096, 3100].includes(type)) {
      categories.mid.push(record);
    } else if ([3093, 3097, 3101].includes(type)) {
      categories.low.push(record);
    } else {
      categories.other.push(record);
    }
  });

  return categories;
}
```

## ✅ 验证清单

- [ ] 时间字段名称已修正为 `issue_time`
- [ ] 违规类型映射包含所有58个类型
- [ ] 原始数值字段保持不变
- [ ] 描述字段命名规范（`violation_type_description`）
- [ ] 未知数值正确处理为 `Unknown(数值)` 格式
- [ ] 时间字段正确转换为UTC时间字符串
- [ ] 支持分页数据的批量转换
- [ ] 前端正确使用描述字段进行显示

这个修正和映射转换功能大大提高了处罚分历史数据的可读性，让开发者和用户都能更容易理解各种违规类型的具体含义和处罚记录的详细信息。
