# 店铺账户健康监控 UI 设计文档

## 📋 概述

基于 Shopee 账户健康 API，设计了一个直观的店铺健康监控界面，帮助用户快速识别和处理店铺问题。

## 🎯 设计目标

1. **突出处罚信息** - 重点展示正在进行的处罚，使用醒目的颜色和图标
2. **直观的健康状态** - 通过颜色编码快速识别店铺状态（正常/警告/异常）
3. **详细的问题分析** - 提供深入的问题根因分析和改进建议
4. **响应式设计** - 适配不同屏幕尺寸，支持移动端查看

## 🎨 界面布局

### 1. 主界面结构

```
┌─────────────────────────────────────────────────────────┐
│ 操作区域                                                  │
│ ├─ 上传按钮                                              │
│ ├─ 店铺选择                                              │
│ └─ 指标类型选择                                          │
├─────────────────────────────────────────────────────────┤
│ 概览统计                                                  │
│ ├─ 总店铺数  ├─ 有处罚店铺  ├─ 总处罚分  ├─ 平均表现分    │
├─────────────────────────────────────────────────────────┤
│ 店铺健康卡片列表                                          │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│ │   店铺A     │ │   店铺B     │ │   店铺C     │        │
│ │ 状态：警告   │ │ 状态：正常   │ │ 状态：异常   │        │
│ └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
```

### 2. 店铺健康卡片设计

每个店铺卡片包含：

- **状态指示器** - 左侧彩色边框表示健康状态
- **基本信息** - 店铺名称、ID、状态标签
- **关键指标** - 表现分、处罚分、等级
- **处罚信息** - 正在进行的处罚（最多显示2项）
- **问题概览** - 问题商品数量、延迟订单数量
- **问题详情预览** - 最新问题商品、最严重延迟订单
- **表现指标** - 关键指标预览（最多显示3项）
- **操作按钮** - 查看详情按钮

### 3. 详情弹窗设计

```
┌─────────────────────────────────────────────────────────┐
│ 店铺健康详情                                    [×]      │
├─────────────────────────────────────────────────────────┤
│ 店铺基本信息区域                                          │
│ ├─ 店铺名称、ID                                          │
│ └─ 健康状态、最后更新时间                                │
├─────────────────────────────────────────────────────────┤
│ 🚨 处罚信息                                              │
│ ├─ 处罚分数概览（4个指标卡片）                            │
│ ├─ 正在进行的处罚列表                                    │
│ ├─ 📋 处罚分历史记录表格                                 │
│ ├─ ⚖️ 处罚历史记录表格                                  │
│ ├─ 🛍️ 问题商品列表表格                                  │
│ └─ 📦 延迟发货订单表格                                   │
├─────────────────────────────────────────────────────────┤
│ 📊 表现指标                                              │
│ ├─ 整体表现概览（3个指标卡片）                            │
│ └─ 详细指标列表（包含改进建议）                          │
└─────────────────────────────────────────────────────────┘
```

## 🎨 视觉设计规范

### 颜色系统

- **正常状态**: `#52c41a` (绿色)
- **警告状态**: `#faad14` (橙色)
- **异常状态**: `#ff4d4f` (红色)
- **主色调**: `#1890ff` (蓝色)

### 处罚严重程度

- **轻微**: `#faad14` (橙色)
- **中等**: `#fa8c16` (深橙色)
- **严重**: `#ff4d4f` (红色)

### 表现等级

- **优秀**: `#52c41a` (绿色)
- **良好**: `#73d13d` (浅绿色)
- **一般**: `#faad14` (橙色)
- **较差**: `#ff4d4f` (红色)

### 指标状态

- **良好**: `#52c41a` (绿色)
- **警告**: `#faad14` (橙色)
- **严重**: `#ff4d4f` (红色)

## 📱 交互设计

### 1. 状态反馈

- **加载状态** - 显示加载动画和进度提示
- **错误状态** - 显示错误信息和重试按钮
- **空数据状态** - 显示友好的空状态提示

### 2. 操作流程

1. 选择店铺 → 选择指标类型 → 自动加载数据
2. 查看概览统计 → 浏览店铺卡片 → 点击查看详情
3. 在详情弹窗中查看完整的处罚和表现信息

### 3. 响应式适配

- **桌面端** - 卡片网格布局，每行3-4个卡片
- **平板端** - 卡片网格布局，每行2-3个卡片
- **移动端** - 单列布局，卡片堆叠显示

## 🔧 技术实现

### 1. 组件结构

```
ShopeeStockMonitor.vue
├─ 店铺选择组件
├─ 指标类型选择组件
├─ 概览统计组件
├─ 店铺健康卡片列表
└─ 详情弹窗组件
```

### 2. 数据流

```
用户选择 → 触发API调用 → 更新响应式数据 → 渲染界面
```

### 3. 状态管理

- `shopHealthData` - 店铺健康数据
- `shopHealthLoading` - 加载状态
- `shopHealthError` - 错误信息
- `healthDetailVisible` - 详情弹窗显示状态
- `selectedShopDetail` - 选中的店铺详情

## 📊 测试数据

已集成完整的测试数据，包含：

- **2个测试店铺**（一个有处罚，一个正常）
- **完整的处罚信息**（处罚分数、正在进行的处罚）
- **详细的表现指标**（延迟发货率、不履行率、店铺评级等）
- **处罚分历史记录**（3条记录，包含违规类型和分数变化）
- **处罚历史记录**（3条记录，包含不同类型的处罚）
- **问题商品列表**（3个问题商品，包含禁售、信息不完整、政策违规）
- **延迟订单列表**（5个延迟订单，延迟天数从1-5天不等）

## 🚀 使用方法

1. 在指标类型下拉框中选择"店铺账户健康"
2. 选择要监控的店铺
3. 系统自动加载并显示店铺健康数据
4. 点击"查看详情"按钮查看完整信息

## 📈 功能特性

- ✅ **实时健康状态监控** - 直观的状态指示和颜色编码
- ✅ **处罚信息突出显示** - 重点展示正在进行的处罚
- ✅ **表现指标趋势分析** - 显示指标趋势和改进建议
- ✅ **详细的问题列表** - 完整的问题商品和延迟订单列表
- ✅ **处罚历史追踪** - 处罚分历史和处罚记录追踪
- ✅ **响应式设计** - 适配不同屏幕尺寸
- ✅ **详细的数据展示** - 表格形式展示详细数据
- ✅ **友好的交互体验** - 直观的卡片设计和弹窗交互

## 🔍 详细功能说明

### 卡片预览功能

- **问题概览** - 显示问题商品数量和延迟订单数量
- **最新问题商品** - 显示最新的问题商品及其问题类型
- **最严重延迟** - 显示延迟天数最多的订单

### 详情弹窗功能

- **处罚分历史记录表格** - 显示每次被罚分的详细信息
- **处罚历史记录表格** - 显示所有处罚的完整记录
- **问题商品列表表格** - 显示有问题的商品详细信息
- **延迟订单列表表格** - 显示延迟发货的订单详细信息

## 🔮 未来扩展

1. **数据导出** - 支持导出健康报告
2. **告警通知** - 处罚预警和邮件通知
3. **历史趋势** - 健康状态历史趋势图表
4. **批量操作** - 批量处理问题商品
5. **自动化建议** - AI驱动的改进建议
