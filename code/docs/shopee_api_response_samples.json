{"shopee_api_response_samples": {"description": "Shopee API接口返回值样例 - 基于真实API测试结果", "last_updated": "2024-01-20", "interfaces": {"1_shop_penalty": {"api_path": "/api/v2/account_health/shop_penalty", "description": "获取店铺处罚信息", "penalty_points_fields": {"overall_penalty_points": "总处罚分", "non_fulfillment_rate": "未履约处罚分", "late_shipment_rate": "延迟发货处罚分", "listing_violations": "商品违规处罚分", "opfr_violations": "OPFR违规处罚分 - The penalty points caused by orders that failed to be picked up during the scheduled pickup day", "others": "其他处罚分"}, "sample_response": {"error": "", "message": "", "request_id": "e3e3e7f33b08cd8a47078c16ac955300:01000207c1cd4653:000000df00c9b184", "response": {"ongoing_punishment": [], "penalty_points": {"late_shipment_rate": 0, "listing_violations": 0, "non_fulfillment_rate": 0, "opfr_violations": 0, "others": 0, "overall_penalty_points": 0}}}, "sample_with_punishment": {"error": "", "message": "", "request_id": "xxxx", "response": {"penalty_points": {"overall_penalty_points": 8, "non_fulfillment_rate": 2, "late_shipment_rate": 3, "listing_violations": 1, "opfr_violations": 1, "others": 1}, "ongoing_punishment": [{"punishment_name": "deboost", "punishment_tier": 3, "days_left": 21}]}}}, "2_shop_performance": {"api_path": "/api/v2/account_health/get_shop_performance", "description": "获取店铺表现指标", "metric_list_fields": {"current_period": "当前周期数值", "last_period": "上个周期数值", "metric_id": "指标ID", "metric_name": "指标名称", "metric_type": "指标类型 (1=Fulfillment, 2=Listing, 3=Customer Service)", "parent_metric_id": "父指标ID", "target": "目标值对象 {comparator, value}", "unit": "单位类型", "exemption_end_date": "(仅限台湾白名单卖家) 豁免结束日期 - Only for whitelist TW sellers when shop is in POL Shop Whitelist, within Exemption Period, and metric_id is 12 or 15"}, "sample_response": {"error": "", "message": "", "request_id": "086b7ce7cece47d2217cb4ffa0bd9f41", "response": {"metric_list": [{"current_period": null, "last_period": null, "metric_id": 3, "metric_name": "non_fulfillment_rate", "metric_type": 1, "parent_metric_id": 0, "target": {"comparator": "<", "value": 10}, "unit": 2}, {"current_period": 0, "last_period": 0, "metric_id": 53, "metric_name": "other_listing_violations", "metric_type": 2, "parent_metric_id": 0, "target": {"comparator": "<=", "value": 0}, "unit": 1}, {"current_period": 57, "last_period": 57, "metric_id": 11, "metric_name": "response_rate", "metric_type": 3, "parent_metric_id": 0, "target": {"comparator": ">=", "value": 70}, "unit": 2}, {"current_period": 8.5, "last_period": 7.2, "metric_id": 12, "metric_name": "pre_order_listing_rate", "metric_type": 2, "parent_metric_id": 0, "target": {"comparator": "<=", "value": 5}, "unit": 2, "exemption_end_date": "2024-12-31"}, {"current_period": 3, "last_period": 2, "metric_id": 15, "metric_name": "the_amount_of_pre_order_listing", "metric_type": 2, "parent_metric_id": 12, "target": {"comparator": "<", "value": 6}, "unit": 1, "exemption_end_date": "2024-12-31"}], "overall_performance": {"custom_service_failed": 1, "fulfillment_failed": 0, "listing_failed": 0, "rating": 3}}}}, "3_metric_source_detail": {"api_path": "/api/v2/account_health/get_metric_source_detail", "description": "获取指标详细数据", "possible_response_fields": {"metrics_id": "指标ID", "total_count": "总数量", "lsr_order_list": "延迟发货订单列表 (metric_id: 1, 85)", "nfr_order_list": "未履约订单列表 (metric_id: 3, 88)", "cancellation_order_list": "取消订单列表 (metric_id: 42, 91)", "return_refund_order_list": "退换货订单列表 (metric_id: 43, 92)", "fhr_order_list": "快速交接订单列表 (metric_id: 25, 2001, 2002, 2003)", "opfr_day_detail_data_list": "OPFR违规详情列表 (metric_id: 28)", "violation_listing_list": "违规商品列表 (metric_id: 52, 53)", "pre_order_listing_violation_data_list": "预售违规详情列表 (metric_id: 15)", "pre_order_listing_list": "预售商品列表 (metric_id: 12)", "sdd_listing_list": "SDD商品列表 (metric_id: 96)", "ndd_listing_list": "NDD商品列表 (metric_id: 97)"}, "sample_response_with_data": {"error": "", "message": "", "request_id": "e3e3e7f325439861d492269eb3785901:010002a0b7410e5c:0000005427c9774e", "response": {"lsr_order_list": [{"actual_shipping_time": **********, "late_by_days": 9, "order_sn": "220809NFRFE424", "shipping_deadline": 1728933145}, {"actual_shipping_time": 1729737070, "late_by_days": 4, "order_sn": "220809NFQ2M26U", "shipping_deadline": 1729366269}], "metrics_id": 1, "total_count": 13}}, "sample_response_empty": {"error": "", "message": "", "request_id": "e3e3e7f33b0929caa029533203697300:0100022746a704fd:00000070a36fa72e", "response": {"metrics_id": 3, "total_count": 0}}, "sample_response_comprehensive": {"description": "包含多种数据类型的完整示例", "error": "", "message": "", "request_id": "sample_comprehensive_request", "response": {"metrics_id": 1, "total_count": 25, "lsr_order_list": [{"actual_shipping_time": **********, "late_by_days": 9, "order_sn": "220809NFRFE424", "shipping_deadline": 1728933145}], "nfr_order_list": [{"order_sn": "ORDER_NFR_001", "non_fulfillment_type": 2, "detailed_reason": 10005}], "cancellation_order_list": [{"order_sn": "ORDER_CANCEL_001", "cancellation_type": 2, "detailed_reason": 10005}], "return_refund_order_list": [{"order_sn": "ORDER_RETURN_001", "detailed_reason": 10016}], "fhr_order_list": [{"order_sn": "ORDER_FHR_001", "parcel_id": *********, "confirm_time": **********, "handover_time": 1729822346, "handover_deadline": 1729908746}], "opfr_day_detail_data_list": [{"date": "19/10/2024", "scheduled_pickup_num": 48, "failed_pickup_num": 11, "opfr": 23, "target": "49.90%"}], "violation_listing_list": [{"item_id": *********, "detailed_reason": 1, "update_time": **********}], "pre_order_listing_violation_data_list": [{"date": "03/11/2024", "live_listing_count": 100, "pre_order_listing_count": 10, "pre_order_listing_rate": 10, "target": "13.00%"}], "pre_order_listing_list": [{"item_id": *********, "current_pre_order_status": 1}], "sdd_listing_list": [{"item_id": *********, "current_sdd_status": 1}], "ndd_listing_list": [{"item_id": *********, "current_ndd_status": 0}]}}}, "4_penalty_point_history": {"api_path": "/api/v2/account_health/get_penalty_point_history", "description": "获取处罚分历史", "sample_response": {"error": "", "message": "", "request_id": "e3e3e7f32548a9975af4ea6b51bd3c01:01000278f354dc17:00000049413a0a7b", "response": {"penalty_point_list": [{"issue_time": **********, "latest_point_num": 1, "original_point_num": 1, "reference_id": 764883879797459160, "violation_type": 12}, {"issue_time": **********, "latest_point_num": 1, "original_point_num": 1, "reference_id": 764881498321979246, "violation_type": 3064}], "total_count": 8}}}, "5_punishment_history": {"api_path": "/api/v2/account_health/get_punishment_history", "description": "获取处罚历史", "punishment_list_fields": {"end_time": "处罚结束时间 (timestamp)", "issue_time": "处罚发布时间 (timestamp)", "punishment_type": "处罚类型 (int32)", "reason": "处罚原因 (int32)", "reference_id": "参考ID (int64)", "start_time": "处罚开始时间 (timestamp)", "listing_limit": "商品限制信息 (string, 当punishment_type为商品限制类型时)", "order_limit": "订单限制百分比 (string, 当punishment_type为2008 Order Limit时) - Daily Order Limit = X % * L28D ADO"}, "sample_response": {"error": "", "message": "", "request_id": "e3e3e7f32548e92b41e998f5fdaaa601:0100028f2ced96f3:0000004e400ff9e3", "response": {"punishment_list": [{"end_time": **********, "issue_time": **********, "punishment_type": 1110, "reason": 1110, "reference_id": 764883890656512286, "start_time": **********}, {"end_time": **********, "issue_time": **********, "punishment_type": 602, "reason": 3, "reference_id": 764881502478534572, "start_time": **********}, {"end_time": **********, "issue_time": **********, "punishment_type": 2008, "reason": 2008, "reference_id": 764881502478534573, "start_time": **********, "order_limit": "50%"}], "total_count": 5}}}, "6_listings_with_issues": {"api_path": "/api/v2/account_health/get_listings_with_issues", "description": "获取问题商品列表", "sample_response": {"error": "", "message": "", "request_id": "e3e3e7f32549047c2083b6f047624c01:0100020ab84cd22c:0000007ad1030734", "response": {"listing_list": [{"item_id": *********, "reason": 1}, {"item_id": *********, "reason": 2}, {"item_id": *********, "reason": 3}, {"item_id": *********, "reason": 4}, {"item_id": *********, "reason": 5}, {"item_id": *********, "reason": 6}, {"item_id": *********, "reason": 7}], "total_count": 7}}}, "7_late_orders": {"api_path": "/api/v2/account_health/get_late_orders", "description": "获取延迟订单列表", "sample_response": {"error": "", "message": "", "request_id": "e3e3e7f32549cec1ed79152920795b01:0100029e3921e1ec:000000f2bd9ada23", "response": {"late_order_list": [{"late_by_days": 1, "order_sn": "220809NFFEFF12", "shipping_deadline": **********}, {"late_by_days": 1, "order_sn": "220809NF5W9WT1", "shipping_deadline": **********}, {"late_by_days": 1, "order_sn": "220809NF57C0G7", "shipping_deadline": **********}, {"late_by_days": 1, "order_sn": "220809NF4HDXGV", "shipping_deadline": **********}, {"late_by_days": 1, "order_sn": "220809NEJN7FC3", "shipping_deadline": **********}], "total_count": 19}}}}, "function_return_structures": {"description": "我们的函数返回结构（经过处理后）", "get_penalty_point_history_return": {"description": "处罚分历史函数返回结构", "structure": {"penalty_point_list": [{"issue_time": "2024-10-11T10:35:27Z", "latest_point_num": 1, "original_point_num": 1, "reference_id": 764883879797459160, "violation_type": 12, "violation_type_description": "Listing Violations"}], "total_count": 8}}, "get_punishment_history_return": {"description": "处罚历史函数返回结构", "structure": {"punishment_list": [{"end_time": "2024-11-08T10:35:30Z", "issue_time": "2024-10-11T10:35:30Z", "punishment_type": 1110, "punishment_type_description": "Listing Limit is reduced", "reason": 1110, "reason_description": "Listing Limit Tier 2", "reference_id": 764883890656512286, "start_time": "2024-10-11T10:35:30Z"}], "total_count": 5}}, "get_listings_with_issues_return": {"description": "问题商品函数返回结构", "structure": {"listing_list": [{"item_id": *********, "reason": 1, "reason_description": "Prohibited"}, {"item_id": *********, "reason": 2, "reason_description": "Counterfeit"}], "total_count": 7}}, "get_late_orders_return": {"description": "延迟订单函数返回结构", "structure": {"late_order_list": [{"late_by_days": 1, "order_sn": "220809NFFEFF12", "shipping_deadline": "2022-08-11T10:29:39Z"}], "total_count": 19}}, "get_metric_source_detail_return": {"description": "指标详情函数返回结构", "structure": {"metrics_id": 1, "metric_id": 1, "lsr_order_list": [{"actual_shipping_time": "2024-10-24T10:32:26Z", "late_by_days": 9, "order_sn": "220809NFRFE424", "shipping_deadline": "2024-10-14T10:32:25Z"}], "total_count": 13}}}, "final_api_response": {"description": "我们的shopee_shop_account_health接口最终返回结构", "api_path": "/shopee-shop-account-health", "sample_response": {"success": true, "message": "查询成功", "data": {"shops": [{"shop_id": *********, "shop_name": "测试店铺A", "penalty_info": {"penalty_points": {"overall_penalty_points": 8, "non_fulfillment_rate": 2, "late_shipment_rate": 3, "listing_violations": 1, "opfr_violations": 1, "others": 1}, "ongoing_punishment": [{"punishment_name": "deboost", "punishment_tier": 3, "days_left": 16}], "has_ongoing_punishment": true}, "performance_overview": {"overall_performance": {"rating": 3, "rating_description": "Good", "fulfillment_failed": 2, "listing_failed": 1, "custom_service_failed": 0}, "metric_count": 15, "last_updated": null}, "health_summary": {"status": "warning", "total_penalty_points": 8}}], "summary": {"total_shops": 1, "shops_with_punishment": 1}}}}}}