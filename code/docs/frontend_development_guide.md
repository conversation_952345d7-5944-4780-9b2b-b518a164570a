# 前端开发指南 - 店铺账户健康监控接口

## 📋 返回值样例说明

基于接口设计，我提供了一个完整的返回值样例 (`api_response_example.json`)，包含两个店铺的数据：

- **店铺A**: 有正在进行的处罚，包含完整的问题分析数据
- **店铺B**: 无处罚，仅包含基础信息

## 🎯 核心数据结构解析

### 1. 店铺基础信息

```javascript
const shop = response.data.shops[0];
console.log(`店铺ID: ${shop.shop_id}`);
console.log(`店铺名称: ${shop.shop_name}`);
```

### 2. 处罚状态检测

```javascript
// 检查是否有正在进行的处罚
if (shop.penalty_info.has_ongoing_punishment) {
  console.log('⚠️ 店铺有正在进行的处罚');

  // 遍历所有处罚
  shop.penalty_info.ongoing_punishment.forEach((punishment) => {
    console.log(`处罚类型: ${punishment.punishment_type}`);
    console.log(`开始时间: ${punishment.start_date}`);
    console.log(`结束时间: ${punishment.end_date}`);
    console.log(`处罚原因: ${punishment.reason}`);
  });
} else {
  console.log('✅ 店铺状态正常');
}
```

### 3. 处罚详细信息（仅在有处罚时存在）

```javascript
if (shop.penalty_details) {
  // 处罚分历史
  const penaltyHistory = shop.penalty_details.penalty_point_history;
  console.log(`处罚分变化记录: ${penaltyHistory.length}条`);
  penaltyHistory.forEach((record) => {
    console.log(`违规类型: ${record.violation_type_description}`);
    console.log(`处罚分: ${record.latest_point_num}分`);
    console.log(`发放时间: ${record.issue_time}`);
  });

  // 处罚历史
  const punishmentHistory = shop.penalty_details.punishment_history;
  console.log(`处罚历史记录: ${punishmentHistory.length}条`);
  punishmentHistory.forEach((record) => {
    console.log(`处罚类型: ${record.punishment_type_description}`);
    console.log(`处罚原因: ${record.reason_description}`);
    console.log(`持续时间: ${record.start_time} - ${record.end_time}`);
  });

  // 问题商品
  const problemItems = shop.penalty_details.listings_with_issues;
  console.log(`问题商品数量: ${problemItems.length}个`);

  // 延迟订单
  const lateOrders = shop.penalty_details.late_orders;
  console.log(`延迟订单数量: ${lateOrders.length}个`);
}
```

## 🎨 前端展示组件示例

### 1. 店铺状态卡片组件

```jsx
function ShopHealthCard({ shop }) {
  const getStatusColor = (status) => {
    switch (status) {
      case 'normal':
        return 'green';
      case 'warning':
        return 'orange';
      case 'error':
        return 'red';
      default:
        return 'gray';
    }
  };

  return (
    <div className="shop-health-card">
      <div className="header">
        <h3>{shop.shop_name}</h3>
        <span
          className={`status-badge ${getStatusColor(shop.health_summary.status)}`}
        >
          {shop.health_summary.status === 'warning' ? '有处罚' : '正常'}
        </span>
      </div>

      {shop.penalty_info.has_ongoing_punishment && (
        <div className="punishment-alert">
          <h4>🚨 正在进行的处罚</h4>
          {shop.penalty_info.ongoing_punishment.map((punishment) => (
            <div key={punishment.punishment_id} className="punishment-item">
              <span className="type">{punishment.punishment_type}</span>
              <span className="reason">{punishment.reason}</span>
              <span className="end-date">
                至 {new Date(punishment.end_date).toLocaleDateString()}
              </span>
            </div>
          ))}
        </div>
      )}

      <div className="summary">
        <div>处罚分: {shop.health_summary.total_penalty_points}</div>
        <div>表现分: {shop.performance_overview.overall_score}</div>
      </div>
    </div>
  );
}
```

### 2. 问题分析详情组件

```jsx
function ProblemAnalysis({ penaltyDetails }) {
  if (!penaltyDetails) return null;

  return (
    <div className="problem-analysis">
      <h3>问题分析详情</h3>

      {/* 处罚分历史 */}
      {penaltyDetails.penalty_point_history.length > 0 && (
        <div className="section">
          <h4>
            📊 处罚分历史 ({penaltyDetails.penalty_point_history.length}条)
          </h4>
          {penaltyDetails.penalty_point_history.map((record) => (
            <div key={record.reference_id} className="penalty-point-record">
              <span className="violation-type">
                {record.violation_type_description}
              </span>
              <span className="points">{record.latest_point_num}分</span>
              <span className="date">
                {new Date(record.issue_time).toLocaleDateString()}
              </span>
              {record.latest_point_num !== record.original_point_num && (
                <span className="adjusted">已调整</span>
              )}
            </div>
          ))}
        </div>
      )}

      {/* 处罚历史 */}
      {penaltyDetails.punishment_history.length > 0 && (
        <div className="section">
          <h4>📋 处罚历史 ({penaltyDetails.punishment_history.length}条)</h4>
          {penaltyDetails.punishment_history.map((punishment) => (
            <div key={punishment.reference_id} className="punishment-history">
              <span className="type">
                {punishment.punishment_type_description}
              </span>
              <span className="reason">{punishment.reason_description}</span>
              <span className="duration">
                {new Date(punishment.start_time).toLocaleDateString()} -{' '}
                {new Date(punishment.end_time).toLocaleDateString()}
              </span>
              {punishment.listing_limit && (
                <span className="limit">
                  商品限制: {punishment.listing_limit}
                </span>
              )}
              {punishment.order_limit && (
                <span className="limit">
                  订单限制: {punishment.order_limit}
                </span>
              )}
            </div>
          ))}
        </div>
      )}

      {/* 问题商品 */}
      {penaltyDetails.listings_with_issues.length > 0 && (
        <div className="section">
          <h4>🛍️ 问题商品 ({penaltyDetails.listings_with_issues.length}个)</h4>
          {penaltyDetails.listings_with_issues.map((item) => (
            <div key={item.item_id} className="problem-item">
              <span className="item-name">{item.item_name}</span>
              <span className="issue-type">
                {item.detailed_reason_description}
              </span>
              <span className="update-time">
                {new Date(item.update_time).toLocaleDateString()}
              </span>
            </div>
          ))}
        </div>
      )}

      {/* 延迟订单 */}
      {penaltyDetails.late_orders.length > 0 && (
        <div className="section">
          <h4>📦 延迟订单 ({penaltyDetails.late_orders.length}个)</h4>
          {penaltyDetails.late_orders.map((order) => (
            <div key={order.order_sn} className="late-order">
              <span className="order-sn">{order.order_sn}</span>
              <span className="delay">延迟{order.late_by_days}天</span>
              <span className="deadline">
                截止: {new Date(order.shipping_deadline).toLocaleDateString()}
              </span>
              <span className="actual">
                实际:{' '}
                {new Date(order.actual_shipping_time).toLocaleDateString()}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
```

## 📊 数据处理工具函数

### 1. 状态判断函数

```javascript
// 判断店铺整体健康状态
function getShopHealthLevel(shop) {
  if (shop.penalty_info.has_ongoing_punishment) {
    return 'critical';
  }

  if (shop.health_summary.total_penalty_points > 20) {
    return 'warning';
  }

  if (shop.performance_overview.overall_score < 70) {
    return 'warning';
  }

  return 'good';
}

// 获取处罚剩余天数
function getRemainingDays(endDate) {
  const end = new Date(endDate);
  const now = new Date();
  const diffTime = end - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
}
```

### 2. 数据统计函数

```javascript
// 统计所有店铺的处罚情况
function getPunishmentSummary(shops) {
  return {
    totalShops: shops.length,
    shopsWithPunishment: shops.filter(
      (s) => s.penalty_info.has_ongoing_punishment,
    ).length,
    totalPenaltyPoints: shops.reduce(
      (sum, s) => sum + s.health_summary.total_penalty_points,
      0,
    ),
    avgPerformanceScore:
      shops.reduce((sum, s) => sum + s.performance_overview.overall_score, 0) /
      shops.length,
  };
}
```

## 🔔 告警处理示例

```javascript
// 检查需要告警的店铺
function checkAlerts(shops) {
  const alerts = [];

  shops.forEach((shop) => {
    // 有正在进行的处罚
    if (shop.penalty_info.has_ongoing_punishment) {
      shop.penalty_info.ongoing_punishment.forEach((punishment) => {
        const remainingDays = getRemainingDays(punishment.end_date);
        alerts.push({
          type: 'punishment',
          shopId: shop.shop_id,
          shopName: shop.shop_name,
          message: `${punishment.punishment_type} 还剩${remainingDays}天`,
          severity: punishment.severity,
          endDate: punishment.end_date,
        });
      });
    }

    // 处罚分过高
    if (shop.health_summary.total_penalty_points > 30) {
      alerts.push({
        type: 'high_penalty_points',
        shopId: shop.shop_id,
        shopName: shop.shop_name,
        message: `处罚分过高: ${shop.health_summary.total_penalty_points}分`,
        severity: 'medium',
      });
    }
  });

  return alerts;
}
```

## 📱 响应式设计建议

```css
/* 店铺健康卡片样式 */
.shop-health-card {
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.status-badge.green {
  background: #52c41a;
  color: white;
}
.status-badge.orange {
  background: #fa8c16;
  color: white;
}
.status-badge.red {
  background: #f5222d;
  color: white;
}

.punishment-alert {
  background: #fff2e8;
  border: 1px solid #ffbb96;
  border-radius: 4px;
  padding: 12px;
  margin: 12px 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .shop-health-card {
    padding: 12px;
  }

  .punishment-item {
    flex-direction: column;
    align-items: flex-start;
  }
}
```

这个返回值样例和开发指南为前端开发提供了完整的数据结构参考和实现示例，可以直接用于开发和测试。
