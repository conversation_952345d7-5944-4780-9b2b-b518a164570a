# Shopee账户健康接口综合映射指南

## 📋 概述

本文档汇总了所有Shopee账户健康相关接口的数据映射转换规则，确保返回值结构准确反映API文档规范，并提供人类可读的描述字段。

## 🔄 接口映射转换总览

### 1. get_shop_performance（店铺表现）

#### 映射字段

- `rating` → `rating_description`
- `metric_type` → `metric_type_description`
- `metric_id` → `metric_id_description`
- `unit` → `unit_description`

#### 数据结构

```json
{
  "overall_performance": {
    "rating": 2,
    "rating_description": "ImprovementNeeded",
    "fulfillment_failed": 2,
    "listing_failed": 1,
    "custom_service_failed": 0
  },
  "metric_list": [
    {
      "metric_type": 1,
      "metric_type_description": "Fulfillment Performance",
      "metric_id": 1,
      "metric_id_description": "Late Shipment Rate (All Channels)",
      "unit": 2,
      "unit_description": "Percentage",
      "current_period": 8.5,
      "last_period": 7.2,
      "target": {
        "value": 5.0,
        "comparator": "<="
      }
    }
  ]
}
```

### 2. get_penalty_point_history（处罚分历史）

#### 映射字段

- `violation_type` → `violation_type_description`

#### 时间字段

- `issue_time`: 处罚分发放时间

#### 数据结构

```json
{
  "penalty_point_list": [
    {
      "issue_time": "2024-01-15T09:30:00Z",
      "latest_point_num": 15,
      "original_point_num": 15,
      "reference_id": 764539404640322244,
      "violation_type": 9,
      "violation_type_description": "Prohibited Listings"
    }
  ],
  "total_count": 1
}
```

### 3. get_punishment_history（处罚历史）

#### 映射字段

- `punishment_type` → `punishment_type_description`
- `reason` → `reason_description`

#### 时间字段

- `issue_time`: 处罚发放时间
- `start_time`: 处罚开始时间
- `end_time`: 处罚结束时间

#### 数据结构

```json
{
  "punishment_list": [
    {
      "issue_time": "2024-01-15T09:30:00Z",
      "start_time": "2024-01-15T00:00:00Z",
      "end_time": "2024-01-22T23:59:59Z",
      "punishment_type": 104,
      "punishment_type_description": "Listings not displayed in search",
      "reason": 2,
      "reason_description": "Tier 2",
      "reference_id": 764539404640322247,
      "listing_limit": null,
      "order_limit": null
    }
  ],
  "total_count": 1
}
```

### 4. get_metric_source_detail（指标详情）

#### 映射字段

- `non_fulfillment_type` → `non_fulfillment_type_description`
- `cancellation_type` → `cancellation_type_description`
- `detailed_reason` → `detailed_reason_description`
- `current_pre_order_status` → `current_pre_order_status_description`
- `current_sdd_status` → `current_sdd_status_description`
- `current_ndd_status` → `current_ndd_status_description`

#### 时间字段转换

- `shipping_deadline`, `actual_shipping_time` (lsr_order_list)
- `confirm_time`, `handover_time`, `handover_deadline` (fhr_order_list)
- `update_time` (violation_listing_list)

#### 数据结构示例

```json
{
  "metric_id": 3,
  "nfr_order_list": [
    {
      "order_sn": "ORDER123",
      "non_fulfillment_type": 2,
      "non_fulfillment_type_description": "Seller Cancellation",
      "detailed_reason": 10005,
      "detailed_reason_description": "Out of Stock"
    }
  ],
  "violation_listing_list": [
    {
      "item_id": 123456,
      "detailed_reason": 1,
      "detailed_reason_description": "Prohibited",
      "update_time": "2024-01-15T09:00:00Z"
    }
  ]
}
```

## 🗂️ 完整映射表

### Rating（评级）映射

| 数值 | 描述              |
| ---- | ----------------- |
| 1    | Poor              |
| 2    | ImprovementNeeded |
| 3    | Good              |
| 4    | Excellent         |

### Metric Type（指标类型）映射

| 数值 | 描述                         |
| ---- | ---------------------------- |
| 1    | Fulfillment Performance      |
| 2    | Listing Performance          |
| 3    | Customer Service Performance |

### Unit（单位）映射

| 数值 | 描述       |
| ---- | ---------- |
| 1    | Number     |
| 2    | Percentage |
| 3    | Second     |
| 4    | Day        |
| 5    | Hour       |

### Violation Type（违规类型）映射（部分）

| 数值 | 描述                                   |
| ---- | -------------------------------------- |
| 5    | High Late Shipment Rate                |
| 6    | High Non-fulfilment Rate               |
| 9    | Prohibited Listings                    |
| 10   | Counterfeit / IP infringement          |
| 3090 | Prohibited Listings-Extreme Violations |
| 4130 | Poor Product Quality                   |

### Punishment Type（处罚类型）映射

| 数值 | 描述                                        |
| ---- | ------------------------------------------- |
| 103  | Listings not displayed in category browsing |
| 104  | Listings not displayed in search            |
| 105  | Unable to create new listings               |
| 109  | Account is suspended                        |
| 1109 | Listing Limit is reduced                    |
| 2008 | Order Limit                                 |

### Punishment Reason（处罚原因）映射

| 数值 | 描述                 |
| ---- | -------------------- |
| 1    | Tier 1               |
| 2    | Tier 2               |
| 3    | Tier 3               |
| 4    | Tier 4               |
| 5    | Tier 5               |
| 1109 | Listing Limit Tier 1 |
| 1110 | Listing Limit Tier 2 |
| 1111 | Listing Limit POL    |

## 🕐 时间字段标准化

所有时间字段统一转换为UTC时间字符串格式：`YYYY-MM-DDTHH:mm:ssZ`

### 时间字段清单

- `issue_time`: 发放/发生时间
- `start_time`: 开始时间
- `end_time`: 结束时间
- `last_updated`: 最后更新时间
- `shipping_deadline`: 发货截止时间
- `actual_shipping_time`: 实际发货时间
- `confirm_time`: 确认时间
- `handover_time`: 交接时间
- `update_time`: 更新时间

## 🎯 设计原则

1. **保留原始数值**: 所有原始数值字段保持不变
2. **添加描述字段**: 新增`_description`后缀的可读字段
3. **时间标准化**: 统一转换为UTC时间字符串格式
4. **向后兼容**: 不影响现有数据结构
5. **错误处理**: 未知数值返回`Unknown(数值)`格式
6. **API文档对齐**: 严格按照Shopee API文档规范

## 🔧 前端使用建议

### 1. 优先显示描述字段

```javascript
function getDisplayValue(item, field) {
  const descriptionField = `${field}_description`;
  return item[descriptionField] || item[field] || 'Unknown';
}
```

### 2. 时间处理

```javascript
function formatTime(utcTimeString) {
  return new Date(utcTimeString).toLocaleString('zh-CN');
}
```

### 3. 状态判断

```javascript
function isPunishmentActive(punishment) {
  return new Date(punishment.end_time) > new Date();
}
```

## ✅ 验证清单

- [ ] 所有数值字段都有对应的描述字段
- [ ] 时间字段都转换为UTC字符串格式
- [ ] 字段名称符合API文档规范
- [ ] 数据结构保持完整性
- [ ] 映射表覆盖所有可能的数值
- [ ] 前端能正确解析和显示数据

这个综合映射指南确保了所有接口返回的数据既符合Shopee API文档规范，又具有良好的可读性和可用性。
