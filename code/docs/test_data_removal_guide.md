# 测试数据删除指南

## 📋 概述

我已经在 `/api/shopee-shop-account-health` 接口中添加了完整的测试数据，用于前端开发和测试。测试完成后，请按照以下步骤删除测试数据，恢复正常的业务逻辑。

## 🗑️ 删除步骤

### 1. 找到测试数据代码块

在文件 `code/app/routers/biz_routes/feishu_api_routes.py` 中，找到以下代码块：

```python
# ========== 临时测试数据 - 前端测试完成后删除 ==========
test_data = []
for i, shop_id in enumerate(shop_ids):
    # ... 大量测试数据 ...

# 返回测试数据
return schemas.StandardResponse(
    success=True,
    message="success (测试数据)",
    data=test_data,
    status_code=200
)
# ========== 测试数据结束 ==========
```

### 2. 删除整个测试数据代码块

删除从 `# ========== 临时测试数据` 开始到 `# ========== 测试数据结束 ==========` 结束的所有代码。

### 3. 确认删除的具体行数

测试数据代码块大约在第 1666-1958 行之间，包括：
- 测试数据生成逻辑
- 返回测试数据的语句
- 所有相关的注释

### 4. 验证删除结果

删除后，接口应该继续执行原有的业务逻辑：

```python
# 获取refresh参数，默认为False
refresh = body.get("refresh", False)

# 获取是否需要详细指标信息的参数
include_metric_details = body.get("include_metric_details", True)

# 获取所有店铺的token信息
shop_tokens = db.query(SHopeeAuthToken).filter(SHopeeAuthToken.shop_id.in_(shop_ids)).all()
# ... 继续原有逻辑
```

## 📊 测试数据说明

### 测试数据包含的内容：

1. **多店铺支持**: 根据请求的shop_ids生成对应数量的测试数据
2. **两种状态**: 第一个店铺有处罚，其他店铺正常
3. **完整字段**: 包含所有接口字段和枚举值转换
4. **真实数据结构**: 完全符合实际API返回格式

### 有处罚的店铺数据包含：
- ✅ penalty_points: 25分处罚分
- ✅ ongoing_punishment: 2个正在进行的处罚
- ✅ performance_overview: ImprovementNeeded评级
- ✅ metrics: 3个指标详情（包含detail数据）
- ✅ penalty_details: 完整的处罚详情
  - penalty_point_history: 3条处罚分历史
  - punishment_history: 3条处罚历史
  - listings_with_issues: 3个问题商品
  - late_orders: 4个延迟订单

### 正常店铺数据包含：
- ✅ penalty_points: 5分处罚分
- ✅ ongoing_punishment: 空数组
- ✅ performance_overview: Good评级
- ✅ metrics: 3个指标详情
- ✅ 无penalty_details字段

## 🧪 测试建议

### 前端测试要点：

1. **多店铺展示**: 测试多个shop_id的数据展示
2. **状态区分**: 验证有处罚和无处罚店铺的不同展示
3. **字段完整性**: 确认所有字段都能正确显示
4. **枚举值显示**: 验证描述字段的正确显示
5. **时间格式**: 确认时间字段的格式化显示
6. **条件渲染**: 测试penalty_details的条件显示

### 测试用例：

```javascript
// 测试请求
{
  "shop_ids": [123456789, 987654321, 555666777],
  "refresh": false,
  "include_metric_details": true
}

// 预期结果：
// - 3个店铺数据
// - 第一个店铺有完整的处罚信息
// - 其他店铺状态正常
// - 所有枚举值都有对应的描述字段
```

## ⚠️ 注意事项

1. **完整删除**: 确保删除所有测试相关代码，不要遗留
2. **语法检查**: 删除后运行语法检查确保代码正确
3. **功能测试**: 删除后测试接口是否能正常调用真实API
4. **备份建议**: 删除前可以备份测试数据代码，以备后续需要

## 🔄 删除后的验证

删除测试数据后，接口应该：
- ✅ 正常接收refresh参数
- ✅ refresh=true时调用Shopee API
- ✅ refresh=false时查询数据库缓存
- ✅ 返回真实的店铺数据
- ✅ 保持所有枚举值转换功能

## ⚠️ 重要更新

`query_time` 字段已完全移除，因为它不是Shopee接口返回的字段：

1. **店铺级别**: 移除了每个店铺数据中的 `query_time` 字段
2. **summary级别**: 移除了汇总信息中的 `query_time` 字段

现在的数据结构为：

```json
{
  "data": {
    "shops": [
      {
        "shop_id": 123456789,
        "shop_name": "测试店铺",
        // 没有 query_time 字段
        "penalty_info": { ... },
        "performance_overview": { ... }
      }
    ],
    "summary": {
      "total_shops": 1,
      "shops_with_punishment": 0
      // 没有 query_time 字段
    }
  }
}
```

删除完成后，请删除本说明文档。
