# 店铺表现数据映射转换文档

## 📋 概述

根据Shopee API文档，`get_shop_performance`接口返回的数据中包含数值型的枚举字段，我们的系统会自动将这些数值转换为对应的描述字符串，以提高数据的可读性。

## 🔄 映射转换字段

### 1. Rating（评级）

**字段**: `rating`, `overall_rating`  
**描述**: 店铺或指标的整体表现评级

| 数值 | 描述字符串        | 说明     |
| ---- | ----------------- | -------- |
| 1    | Poor              | 差       |
| 2    | ImprovementNeeded | 需要改进 |
| 3    | Good              | 良好     |
| 4    | Excellent         | 优秀     |

**转换示例**:

```json
{
  "overall_rating": 3,
  "overall_rating_description": "Good"
}
```

### 2. Metric Type（指标类型）

**字段**: `metric_type`  
**描述**: 指标所属的类型分类

| 数值 | 描述字符串                   | 说明     |
| ---- | ---------------------------- | -------- |
| 1    | Fulfillment Performance      | 履约表现 |
| 2    | Listing Performance          | 商品表现 |
| 3    | Customer Service Performance | 客服表现 |

**转换示例**:

```json
{
  "metric_type": 1,
  "metric_type_description": "Fulfillment Performance"
}
```

### 3. Metric ID（指标ID）

**字段**: `metric_id`  
**描述**: 具体指标的唯一标识符

| 数值 | 描述字符串                                  | 说明                   |
| ---- | ------------------------------------------- | ---------------------- |
| -1   | Non-Responded Chats                         | 未回复聊天（指标组）   |
| 1    | Late Shipment Rate (All Channels)           | 延迟发货率（所有渠道） |
| 3    | Non-Fulfilment Rate (All Channels)          | 未履约率（所有渠道）   |
| 4    | Preparation Time                            | 准备时间               |
| 11   | Chat Response Rate                          | 聊天回复率             |
| 12   | Pre-order Listing %                         | 预售商品百分比         |
| 15   | Days of Pre-order Listing Violation         | 预售商品违规天数       |
| 21   | Response Time                               | 响应时间               |
| 22   | Shop Rating                                 | 店铺评分               |
| 23   | No. of Non-Responded Chats                  | 未回复聊天数量         |
| 25   | Fast Handover Rate                          | 快速交接率             |
| 27   | On-time Pickup Failure Rate                 | 准时取货失败率         |
| 28   | On-time Pickup Failure Rate Violation Value | 准时取货失败率违规值   |
| 29   | Average Response Time                       | 平均响应时间           |
| 42   | Cancellation Rate (All Channels)            | 取消率（所有渠道）     |
| 43   | Return-refund Rate (All Channels)           | 退换货率（所有渠道）   |
| 52   | Severe Listing Violations                   | 严重商品违规           |
| 53   | Other Listing Violations                    | 其他商品违规           |
| 54   | Prohibited Listings                         | 禁售商品               |
| 55   | Counterfeit/IP infringement                 | 假冒/知识产权侵权      |
| 56   | Spam Listings                               | 垃圾商品               |
| 85   | Late Shipment Rate (NDD)                    | 延迟发货率（次日达）   |
| 88   | Non-fulfilment Rate (NDD)                   | 未履约率（次日达）     |
| 91   | Cancellation Rate (NDD)                     | 取消率（次日达）       |
| 92   | Return-refund Rate (NDD)                    | 退换货率（次日达）     |
| 95   | Customer Satisfaction                       | 客户满意度             |
| 96   | % SDD Listings                              | 当日达商品百分比       |
| 97   | % NDD Listings                              | 次日达商品百分比       |
| 2001 | Fast Handover Rate - SLS                    | 快速交接率 - SLS       |
| 2002 | Fast Handover Rate - FBS                    | 快速交接率 - FBS       |
| 2003 | Fast Handover Rate - 3PF                    | 快速交接率 - 3PF       |
| 2011 | Poor Quality Products                       | 质量差的商品           |

**转换示例**:

```json
{
  "metric_id": 22,
  "metric_id_description": "Shop Rating"
}
```

### 4. Unit（单位）

**字段**: `unit`  
**描述**: 指标值的计量单位

| 数值 | 描述字符串 | 说明   |
| ---- | ---------- | ------ |
| 1    | Number     | 数量   |
| 2    | Percentage | 百分比 |
| 3    | Second     | 秒     |
| 4    | Day        | 天     |
| 5    | Hour       | 小时   |

**转换示例**:

```json
{
  "unit": 2,
  "unit_description": "Percentage"
}
```

## 📊 完整转换示例

### 转换前（Shopee API原始返回）:

```json
{
  "response": {
    "overall_performance": {
      "rating": 2,
      "fulfillment_failed": 2,
      "listing_failed": 1,
      "custom_service_failed": 0
    },
    "metric_list": [
      {
        "metric_type": 1,
        "metric_id": 1,
        "parent_metric_id": null,
        "metric_name": "Late Shipment Rate (All Channels)",
        "current_period": 8.5,
        "last_period": 7.2,
        "unit": 2,
        "target": {
          "value": 5.0,
          "comparator": "<="
        },
        "exemption_end_date": null
      }
    ]
  }
}
```

### 转换后（我们的接口返回）:

```json
{
  "overall_performance": {
    "rating": 2,
    "rating_description": "ImprovementNeeded",
    "fulfillment_failed": 2,
    "listing_failed": 1,
    "custom_service_failed": 0
  },
  "metric_list": [
    {
      "metric_type": 1,
      "metric_type_description": "Fulfillment Performance",
      "metric_id": 1,
      "metric_id_description": "Late Shipment Rate (All Channels)",
      "parent_metric_id": null,
      "metric_name": "Late Shipment Rate (All Channels)",
      "current_period": 8.5,
      "last_period": 7.2,
      "unit": 2,
      "unit_description": "Percentage",
      "target": {
        "value": 5.0,
        "comparator": "<="
      },
      "exemption_end_date": null
    }
  ]
}
```

## 🎯 设计原则

1. **保留原始数值**: 原始的数值字段保持不变，便于程序处理
2. **添加描述字段**: 新增 `_description` 后缀的字段，提供人类可读的描述
3. **向后兼容**: 不影响现有的数据结构和字段
4. **错误处理**: 对于未知的数值，返回 `Unknown(数值)` 格式的描述

## 🔧 前端使用建议

### 1. 显示优先级

```javascript
// 优先显示描述字符串，回退到原始数值
function getDisplayValue(item, field) {
  const descriptionField = `${field}_description`;
  return item[descriptionField] || item[field] || 'Unknown';
}

// 使用示例
const ratingDisplay = getDisplayValue(metric, 'rating');
const typeDisplay = getDisplayValue(metric, 'metric_type');
```

### 2. 条件渲染

```jsx
function MetricCard({ metric }) {
  return (
    <div className="metric-card">
      <h3>{metric.metric_id_description || `Metric ${metric.metric_id}`}</h3>
      <div className="rating">
        评级:{' '}
        <span className={`rating-${metric.rating}`}>
          {metric.rating_description}
        </span>
      </div>
      <div className="type">类型: {metric.metric_type_description}</div>
      <div className="unit">单位: {metric.unit_description}</div>
    </div>
  );
}
```

### 3. 数据过滤和排序

```javascript
// 按指标类型分组
const groupedMetrics = metrics.reduce((groups, metric) => {
  const type = metric.metric_type_description || 'Unknown';
  if (!groups[type]) groups[type] = [];
  groups[type].push(metric);
  return groups;
}, {});

// 按评级排序（Poor -> Excellent）
const sortedByRating = metrics.sort((a, b) => {
  const ratingOrder = { Poor: 1, ImprovementNeeded: 2, Good: 3, Excellent: 4 };
  return ratingOrder[a.rating_description] - ratingOrder[b.rating_description];
});
```

## ✅ 验证清单

- [ ] 所有数值字段都有对应的描述字段
- [ ] 原始数值字段保持不变
- [ ] 描述字段命名规范（原字段名 + `_description`）
- [ ] 未知数值正确处理为 `Unknown(数值)` 格式
- [ ] 前端正确使用描述字段进行显示

这个映射转换功能大大提高了数据的可读性，让前端开发者和最终用户都能更容易理解店铺表现数据的含义。
