# 处罚历史数据映射转换文档

## 📋 概述

`get_punishment_history`接口根据Shopee API文档返回店铺的处罚历史记录。我们的系统会自动将返回数据中的数值型枚举字段转换为对应的描述字符串，并添加时间字段处理，以提高数据的可读性。

## 🔄 主要修正内容

### 1. 时间字段处理

**添加的时间字段**: `issue_time`（处罚发放时间）  
**现有时间字段**: `start_time`（处罚开始时间）, `end_time`（处罚结束时间）

根据API文档，处罚历史记录包含三个时间字段，都需要从时间戳转换为UTC时间字符串。

### 2. 处罚类型映射转换

**字段**: `punishment_type`  
**适用列表**: `punishment_list`

### 3. 处罚原因映射转换

**字段**: `reason`  
**适用列表**: `punishment_list`

## 🗂️ 处罚类型完整映射表

### 基础处罚类型 (100-109)

| 数值 | 描述字符串                                  | 说明                   |
| ---- | ------------------------------------------- | ---------------------- |
| 103  | Listings not displayed in category browsing | 商品不在分类浏览中显示 |
| 104  | Listings not displayed in search            | 商品不在搜索中显示     |
| 105  | Unable to create new listings               | 无法创建新商品         |
| 106  | Unable to edit listings                     | 无法编辑商品           |
| 107  | Unable to join marketing campaigns          | 无法参加营销活动       |
| 108  | No shipping subsidies                       | 无运费补贴             |
| 109  | Account is suspended                        | 账户被暂停             |

### 搜索相关处罚 (600-602)

| 数值 | 描述字符串                                  | 说明                   |
| ---- | ------------------------------------------- | ---------------------- |
| 600  | Listings not displayed in search            | 商品不在搜索中显示     |
| 601  | Shop listings hide from recommendation      | 店铺商品从推荐中隐藏   |
| 602  | Listings not displayed in category browsing | 商品不在分类浏览中显示 |

### 商品限制处罚 (1109-1112)

| 数值 | 描述字符串               | 说明           |
| ---- | ------------------------ | -------------- |
| 1109 | Listing Limit is reduced | 商品限制被减少 |
| 1110 | Listing Limit is reduced | 商品限制被减少 |
| 1111 | Listing Limit is reduced | 商品限制被减少 |
| 1112 | Listing Limit is reduced | 商品限制被减少 |

### 订单限制处罚 (2008)

| 数值 | 描述字符串  | 说明     |
| ---- | ----------- | -------- |
| 2008 | Order Limit | 订单限制 |

## 🗂️ 处罚原因完整映射表

### 等级原因 (1-5)

| 数值 | 描述字符串 | 说明  |
| ---- | ---------- | ----- |
| 1    | Tier 1     | 等级1 |
| 2    | Tier 2     | 等级2 |
| 3    | Tier 3     | 等级3 |
| 4    | Tier 4     | 等级4 |
| 5    | Tier 5     | 等级5 |

### 商品限制等级 (1109-1111)

| 数值 | 描述字符串           | 说明          |
| ---- | -------------------- | ------------- |
| 1109 | Listing Limit Tier 1 | 商品限制等级1 |
| 1110 | Listing Limit Tier 2 | 商品限制等级2 |
| 1111 | Listing Limit POL    | 商品限制POL   |

## 📊 数据结构说明

### API返回的字段结构

```json
{
  "punishment_list": [
    {
      "issue_time": 1728552398, // 处罚发放时间（时间戳）
      "start_time": 1728552400, // 处罚开始时间（时间戳）
      "end_time": 1728638800, // 处罚结束时间（时间戳）
      "punishment_type": 104, // 处罚类型（数值）
      "reason": 2, // 处罚原因（数值）
      "reference_id": 764539404640322244, // 参考ID
      "listing_limit": null, // 商品限制数量
      "order_limit": null // 订单限制百分比
    }
  ],
  "total_count": 1 // 总记录数
}
```

### 转换后的数据结构

```json
{
  "punishment_list": [
    {
      "issue_time": "2024-10-10T07:19:58Z", // 转换为UTC时间字符串
      "start_time": "2024-10-10T07:20:00Z", // 转换为UTC时间字符串
      "end_time": "2024-10-11T07:20:00Z", // 转换为UTC时间字符串
      "punishment_type": 104, // 保留原始数值
      "punishment_type_description": "Listings not displayed in search", // 添加描述字段
      "reason": 2, // 保留原始数值
      "reason_description": "Tier 2", // 添加描述字段
      "reference_id": 764539404640322244,
      "listing_limit": null,
      "order_limit": null
    }
  ],
  "total_count": 1
}
```

## 🔄 转换示例

### 转换前（Shopee API原始返回）:

```json
[
  {
    "punishment_list": [
      {
        "issue_time": 1728552398,
        "start_time": 1728552400,
        "end_time": 1728638800,
        "punishment_type": 1109,
        "reason": 1109,
        "reference_id": 764539404640322244,
        "listing_limit": 100,
        "order_limit": null
      }
    ],
    "total_count": 1
  }
]
```

### 转换后（我们的接口返回）:

```json
[
  {
    "punishment_list": [
      {
        "issue_time": "2024-10-10T07:19:58Z",
        "start_time": "2024-10-10T07:20:00Z",
        "end_time": "2024-10-11T07:20:00Z",
        "punishment_type": 1109,
        "punishment_type_description": "Listing Limit is reduced",
        "reason": 1109,
        "reason_description": "Listing Limit Tier 1",
        "reference_id": 764539404640322244,
        "listing_limit": 100,
        "order_limit": null
      }
    ],
    "total_count": 1
  }
]
```

## 🎯 特殊字段说明

### 1. listing_limit 字段

当 `punishment_type` 为以下值时，返回具体的商品限制数量：

- 1109: Listing Limit is reduced
- 1110: Listing Limit is reduced
- 1111: Listing Limit is reduced
- 1112: Listing Limit is reduced

### 2. order_limit 字段

当 `punishment_type` 为 2008 (Order Limit) 时，返回订单限制百分比字符串。

**计算公式**: Daily Order Limit = X % \* L28D ADO (Average Daily Order of this Shop in Past 28 Days)

## 🎯 设计原则

1. **时间字段完整性**: 处理所有三个时间字段（issue_time, start_time, end_time）
2. **保留原始数值**: 原始的数值字段保持不变，便于程序处理
3. **添加描述字段**: 新增 `_description` 后缀的字段，提供人类可读的描述
4. **向后兼容**: 不影响现有的数据结构和字段
5. **错误处理**: 对于未知的数值，返回 `Unknown(数值)` 格式的描述
6. **分页支持**: 支持分页数据的批量转换

## 🔧 前端使用建议

### 1. 处罚记录展示

```jsx
function PunishmentRecord({ record }) {
  const isActive = new Date(record.end_time) > new Date();

  return (
    <div className={`punishment-record ${isActive ? 'active' : 'expired'}`}>
      <div className="punishment-type">
        <span className="type-badge">{record.punishment_type_description}</span>
        {isActive && <span className="active-indicator">进行中</span>}
      </div>

      <div className="reason">原因: {record.reason_description}</div>

      <div className="duration">
        <span>开始: {new Date(record.start_time).toLocaleString()}</span>
        <span>结束: {new Date(record.end_time).toLocaleString()}</span>
      </div>

      {record.listing_limit && (
        <div className="limit">商品限制: {record.listing_limit}个</div>
      )}

      {record.order_limit && (
        <div className="limit">订单限制: {record.order_limit}</div>
      )}

      <div className="reference">参考ID: {record.reference_id}</div>
    </div>
  );
}
```

### 2. 处罚类型分类展示

```javascript
// 按处罚影响分类
function categorizePunishments(records) {
  const categories = {
    listing: [], // 商品相关处罚
    search: [], // 搜索相关处罚
    account: [], // 账户相关处罚
    marketing: [], // 营销相关处罚
  };

  records.forEach((record) => {
    const type = record.punishment_type;
    if ([103, 105, 106, 602, 1109, 1110, 1111, 1112].includes(type)) {
      categories.listing.push(record);
    } else if ([104, 600, 601].includes(type)) {
      categories.search.push(record);
    } else if ([109].includes(type)) {
      categories.account.push(record);
    } else if ([107, 108].includes(type)) {
      categories.marketing.push(record);
    }
  });

  return categories;
}
```

### 3. 处罚时间线展示

```jsx
function PunishmentTimeline({ records }) {
  const sortedRecords = records.sort(
    (a, b) => new Date(b.issue_time) - new Date(a.issue_time),
  );

  return (
    <div className="punishment-timeline">
      {sortedRecords.map((record) => (
        <div key={record.reference_id} className="timeline-item">
          <div className="timeline-date">
            {new Date(record.issue_time).toLocaleDateString()}
          </div>
          <div className="timeline-content">
            <h4>{record.punishment_type_description}</h4>
            <p>原因: {record.reason_description}</p>
            <p>
              持续时间: {new Date(record.start_time).toLocaleDateString()} -{' '}
              {new Date(record.end_time).toLocaleDateString()}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
}
```

## ✅ 验证清单

- [ ] 时间字段包含 `issue_time`, `start_time`, `end_time`
- [ ] 处罚类型映射包含所有15个类型
- [ ] 处罚原因映射包含所有8个原因
- [ ] 原始数值字段保持不变
- [ ] 描述字段命名规范（`_description` 后缀）
- [ ] 未知数值正确处理为 `Unknown(数值)` 格式
- [ ] 时间字段正确转换为UTC时间字符串
- [ ] 支持分页数据的批量转换
- [ ] 特殊字段 `listing_limit` 和 `order_limit` 正确保留
- [ ] 前端正确使用描述字段进行显示

这个修正和映射转换功能大大提高了处罚历史数据的可读性，让开发者和用户都能更容易理解各种处罚类型、原因的具体含义和处罚记录的详细信息。
