# Shopee店铺账户健康监控接口文档

## 📋 接口概述

**接口路径**: `POST /api/shopee-shop-account-health`  
**功能**: 获取店铺账户健康综合信息，包括处罚状态、表现指标和详细信息  
**设计理念**: 以`shop_penalty`为核心，整合店铺健康相关的所有关键信息

## 🎯 核心特性

1. **🚨 突出正在进行的处罚** - 重点展示ongoing_punishment
2. **📊 综合表现评估** - 整合penalty_points和performance指标
3. **🔍 智能详情获取** - 有处罚时自动获取问题分析详情
4. **⚡ 实时数据** - 直接调用Shopee API获取最新状态
5. **🎯 问题根因分析** - 自动调用相关接口分析处罚原因

## 📝 请求参数

```json
{
  "shop_ids": [123456, 789012], // 必填：店铺ID列表
  "include_metric_details": true // 可选：是否包含指标详情，默认true
}
```

## 📤 响应结构

### 成功响应示例

```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "shops": [
      {
        "shop_id": 123456,
        "shop_name": "测试店铺",

        // 🚨 处罚信息（核心重点）
        "penalty_info": {
          "penalty_points": {
            "total": 15,
            "current_period": 5,
            "previous_period": 10
          },
          "ongoing_punishment": [
            {
              "punishment_type": "listing_ban",
              "start_date": "2024-01-15T00:00:00Z",
              "end_date": "2024-01-22T23:59:59Z",
              "reason": "违规商品",
              "severity": "medium"
            }
          ],
          "has_ongoing_punishment": true
        },

        // 📊 店铺表现概览
        "performance_overview": {
          "overall_score": 75,
          "performance_level": "good",
          "metric_count": 12,
          "last_updated": "2024-01-20T10:30:00Z"
        },

        // 🔍 指标详情（可选）
        "metrics": [
          {
            "metric_id": 1,
            "metric_name": "订单完成率",
            "score": 85,
            "target": 90,
            "status": "warning",
            "detail": {
              "current_value": "85%",
              "trend": "declining",
              "improvement_suggestions": ["提高发货速度", "优化客服响应"]
            }
          }
        ],

        // ⚡ 状态汇总
        "health_summary": {
          "status": "warning", // normal/warning/error
          "total_penalty_points": 15
        },

        // 🔍 处罚详细信息（仅在有正在进行的处罚时返回）
        "penalty_details": {
          "penalty_point_history": [
            {
              "issue_time": "2024-01-15T09:30:00Z",
              "latest_point_num": 5,
              "original_point_num": 5,
              "reference_id": 764539404640322244,
              "violation_type": 9,
              "violation_type_description": "Prohibited Listings"
            }
          ],
          "punishment_history": [
            {
              "issue_time": "2024-01-15T09:30:00Z",
              "start_time": "2024-01-15T00:00:00Z",
              "end_time": "2024-01-22T23:59:59Z",
              "punishment_type": 104,
              "punishment_type_description": "Listings not displayed in search",
              "reason": 2,
              "reason_description": "Tier 2",
              "reference_id": 764539404640322247,
              "listing_limit": null,
              "order_limit": null
            }
          ],
          "listings_with_issues": [
            {
              "item_id": 123456,
              "item_name": "测试商品",
              "detailed_reason": 1,
              "detailed_reason_description": "Prohibited",
              "update_time": "2024-01-15T09:00:00Z"
            }
          ],
          "late_orders": [
            {
              "order_sn": "ORDER123",
              "shipping_deadline": "2024-01-11T23:59:59Z",
              "actual_shipping_time": "2024-01-14T08:20:00Z",
              "late_by_days": 3
            }
          ]
        }
      }
    ],

    // 📈 整体汇总
    "summary": {
      "total_shops": 2,
      "shops_with_punishment": 1
    }
  },
  "status_code": 200
}
```

## 🎨 前端展示建议

### 1. 总览卡片设计

```
┌─────────────────────────────────────┐
│ 🏪 店铺名称                    [⚠️]  │
│ ─────────────────────────────────── │
│ 🚨 正在处罚: 商品下架 (还剩3天)      │
│ 📊 处罚分: 15分 | 表现分: 75分       │
│ 🎯 风险等级: 高风险                 │
│ ─────────────────────────────────── │
│ [查看详情] [处理建议]               │
└─────────────────────────────────────┘
```

### 2. 状态颜色方案

- 🟢 **正常** (normal): 无处罚，表现良好
- 🟡 **警告** (warning): 有正在进行的处罚
- 🔴 **错误** (error): 数据获取失败

### 3. 智能详情展示

- 当检测到有正在进行的处罚时，自动显示详细的问题分析
- 包括处罚历史、问题商品、延迟订单等根因信息
- 帮助用户快速定位和解决问题

## 🔧 使用场景

### 场景1: 日常监控仪表板

```javascript
// 获取所有店铺的基础健康信息
const response = await fetch('/shopee-shop-account-health', {
  method: 'POST',
  body: JSON.stringify({
    shop_ids: [123456, 789012],
    include_metric_details: false, // 仪表板不需要详情
  }),
});
```

### 场景2: 详细分析页面

```javascript
// 获取单个店铺的详细信息
const response = await fetch('/shopee-shop-account-health', {
  method: 'POST',
  body: JSON.stringify({
    shop_ids: [123456],
    include_metric_details: true, // 需要详细的指标信息
  }),
});
```

### 场景3: 告警系统

```javascript
// 检查是否有店铺存在处罚
const data = response.data;
const alertShops = data.shops.filter(
  (shop) => shop.penalty_info.has_ongoing_punishment,
);

// 获取处罚详情进行分析
alertShops.forEach((shop) => {
  if (shop.penalty_details) {
    console.log(`店铺${shop.shop_name}的问题分析:`, {
      处罚历史: shop.penalty_details.punishment_history,
      问题商品: shop.penalty_details.listings_with_issues,
      延迟订单: shop.penalty_details.late_orders,
    });
  }
});
```

## ⚡ 性能优化建议

1. **分层加载**: 先加载基础信息，点击详情时再加载metric_details
2. **缓存策略**: 可以考虑短时间缓存(5-10分钟)，减少API调用
3. **批量查询**: 一次查询多个店铺，减少请求次数
4. **异步处理**: 对于大量店铺，可以考虑异步处理

## 🛠️ 扩展性设计

接口设计考虑了未来扩展：

- 可以轻松添加新的健康指标
- 支持自定义风险等级计算规则
- 可以集成更多Shopee账户健康接口
- 支持个性化的告警阈值设置
