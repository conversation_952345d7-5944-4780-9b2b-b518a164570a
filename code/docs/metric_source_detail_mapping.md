# 指标详情数据映射转换文档

## 📋 概述

`get_metric_source_detail`接口根据Shopee API文档返回不同指标的详细数据，包含多种类型的订单列表和商品列表。我们的系统会自动将这些数据中的数值型枚举字段转换为对应的描述字符串，以提高数据的可读性。

## 🔄 映射转换字段

### 1. Non-fulfillment Type（未履约类型）

**字段**: `non_fulfillment_type`  
**适用列表**: `nfr_order_list`  
**支持的metric_id**: 3, 88

| 数值 | 描述字符串          | 说明     |
| ---- | ------------------- | -------- |
| 1    | System Cancellation | 系统取消 |
| 2    | Seller Cancellation | 卖家取消 |
| 3    | Return Refunds      | 退换货   |

### 2. Cancellation Type（取消类型）

**字段**: `cancellation_type`  
**适用列表**: `cancellation_order_list`  
**支持的metric_id**: 42, 91

| 数值 | 描述字符串          | 说明     |
| ---- | ------------------- | -------- |
| 1    | System Cancellation | 系统取消 |
| 2    | Seller Cancellation | 卖家取消 |

### 3. Detailed Reason（详细原因）

**字段**: `detailed_reason`  
**适用列表**: `nfr_order_list`, `cancellation_order_list`, `return_refund_order_list`

| 数值  | 描述字符串                    | 说明           |
| ----- | ----------------------------- | -------------- |
| 1001  | Return Refund                 | 退换货         |
| 1002  | Parcel Split Cancellation     | 包裹拆分取消   |
| 1003  | First Mile Pick up fail       | 首公里取货失败 |
| 1004  | Order inclusion               | 订单包含       |
| 10005 | Out of Stock                  | 缺货           |
| 10006 | Undeliverable area            | 无法配送区域   |
| 10007 | Cannot support COD            | 不支持货到付款 |
| 10008 | Logistics request cancelled   | 物流请求取消   |
| 10009 | Logistics pickup failed       | 物流取货失败   |
| 10010 | Logistics not ready           | 物流未准备好   |
| 10011 | Inactive seller               | 卖家不活跃     |
| 10012 | Seller did not ship order     | 卖家未发货     |
| 10013 | Order did not reach warehouse | 订单未到达仓库 |
| 10014 | Seller asked to cancel        | 卖家要求取消   |
| 10015 | Non-receipt                   | 未收到货       |
| 10016 | Wrong item                    | 商品错误       |
| 10017 | Damaged item                  | 商品损坏       |
| 10018 | Incomplete product            | 商品不完整     |
| 10019 | Fake item                     | 假货           |
| 10020 | Functional Damage             | 功能损坏       |
| 10021 | Return Refund                 | 退换货         |

### 4. Violation Detailed Reason（违规详细原因）

**字段**: `detailed_reason`  
**适用列表**: `violation_listing_list`  
**支持的metric_id**: 52, 53

| 数值 | 描述字符串                | 说明         |
| ---- | ------------------------- | ------------ |
| 1    | Prohibited                | 禁售商品     |
| 2    | Counterfeit               | 假冒商品     |
| 3    | Spam                      | 垃圾商品     |
| 4    | Inappropriate Image       | 不当图片     |
| 5    | Insufficient Info         | 信息不足     |
| 6    | Mall Listing Improvement  | 商城商品改进 |
| 7    | Other Listing Improvement | 其他商品改进 |
| 8    | PQR Products              | PQR商品      |

### 5. Pre-order Status（预售状态）

**字段**: `current_pre_order_status`  
**适用列表**: `pre_order_listing_list`  
**支持的metric_id**: 12

| 数值 | 描述字符串 | 说明 |
| ---- | ---------- | ---- |
| 1    | Yes        | 是   |
| 2    | No         | 否   |

### 6. SDD Status（当日达状态）

**字段**: `current_sdd_status`  
**适用列表**: `sdd_listing_list`  
**支持的metric_id**: 96

| 数值 | 描述字符串 | 说明 |
| ---- | ---------- | ---- |
| 1    | Yes        | 是   |
| 0    | No         | 否   |

### 7. NDD Status（次日达状态）

**字段**: `current_ndd_status`  
**适用列表**: `ndd_listing_list`  
**支持的metric_id**: 97

| 数值 | 描述字符串 | 说明 |
| ---- | ---------- | ---- |
| 1    | Yes        | 是   |
| 0    | No         | 否   |

## 📊 支持的数据列表类型

### 1. 订单相关列表

| 列表名称 | 说明 | 支持的metric_id | 转换字段 |
| --- | --- | --- | --- |
| `nfr_order_list` | 未履约订单列表 | 3, 88 | `non_fulfillment_type`, `detailed_reason` |
| `cancellation_order_list` | 取消订单列表 | 42, 91 | `cancellation_type`, `detailed_reason` |
| `return_refund_order_list` | 退换货订单列表 | 43, 92 | `detailed_reason` |
| `lsr_order_list` | 延迟发货订单列表 | 1, 85 | 时间字段转换 |
| `fhr_order_list` | 快速交接订单列表 | 25, 2001, 2002, 2003 | 时间字段转换 |

### 2. 商品相关列表

| 列表名称 | 说明 | 支持的metric_id | 转换字段 |
| --- | --- | --- | --- |
| `violation_listing_list` | 违规商品列表 | 52, 53 | `detailed_reason`, 时间字段 |
| `pre_order_listing_list` | 预售商品列表 | 12 | `current_pre_order_status` |
| `sdd_listing_list` | 当日达商品列表 | 96 | `current_sdd_status` |
| `ndd_listing_list` | 次日达商品列表 | 97 | `current_ndd_status` |

### 3. 其他数据列表

| 列表名称 | 说明 | 支持的metric_id | 转换字段 |
| --- | --- | --- | --- |
| `opfr_day_detail_data_list` | OPFR违规详情 | 28 | 无需转换 |
| `pre_order_listing_violation_data_list` | 预售违规详情 | 15 | 无需转换 |

## 🕐 时间字段转换

以下时间字段会自动从UTC时间戳转换为UTC时间字符串：

| 列表 | 时间字段 |
| --- | --- |
| `lsr_order_list` | `shipping_deadline`, `actual_shipping_time` |
| `fhr_order_list` | `confirm_time`, `handover_time`, `handover_deadline` |
| `violation_listing_list` | `update_time` |

## 📝 转换示例

### 转换前（Shopee API原始返回）:

```json
[
  {
    "metric_id": 3,
    "nfr_order_list": [
      {
        "order_sn": "ORDER123",
        "non_fulfillment_type": 2,
        "detailed_reason": 10005
      }
    ],
    "violation_listing_list": [
      {
        "item_id": 123456,
        "detailed_reason": 1,
        "update_time": 1705305600
      }
    ],
    "total_count": 2
  }
]
```

### 转换后（我们的接口返回）:

```json
[
  {
    "metric_id": 3,
    "nfr_order_list": [
      {
        "order_sn": "ORDER123",
        "non_fulfillment_type": 2,
        "non_fulfillment_type_description": "Seller Cancellation",
        "detailed_reason": 10005,
        "detailed_reason_description": "Out of Stock"
      }
    ],
    "violation_listing_list": [
      {
        "item_id": 123456,
        "detailed_reason": 1,
        "detailed_reason_description": "Prohibited",
        "update_time": "2024-01-15T08:00:00Z"
      }
    ],
    "total_count": 2
  }
]
```

## 🎯 设计原则

1. **保留原始数值**: 原始的数值字段保持不变，便于程序处理
2. **添加描述字段**: 新增 `_description` 后缀的字段，提供人类可读的描述
3. **向后兼容**: 不影响现有的数据结构和字段
4. **错误处理**: 对于未知的数值，返回 `Unknown(数值)` 格式的描述
5. **时间标准化**: 所有时间字段统一转换为UTC时间字符串格式

## 🔧 前端使用建议

### 1. 数据展示优先级

```javascript
// 优先显示描述字符串，回退到原始数值
function getDisplayValue(item, field) {
  const descriptionField = `${field}_description`;
  return item[descriptionField] || item[field] || 'Unknown';
}

// 使用示例
const typeDisplay = getDisplayValue(order, 'non_fulfillment_type');
const reasonDisplay = getDisplayValue(order, 'detailed_reason');
```

### 2. 订单状态展示

```jsx
function OrderStatusCard({ order }) {
  return (
    <div className="order-card">
      <h4>订单: {order.order_sn}</h4>
      {order.non_fulfillment_type_description && (
        <div className="status">
          类型:{' '}
          <span className="type-badge">
            {order.non_fulfillment_type_description}
          </span>
        </div>
      )}
      {order.detailed_reason_description && (
        <div className="reason">原因: {order.detailed_reason_description}</div>
      )}
    </div>
  );
}
```

### 3. 商品违规展示

```jsx
function ViolationListingCard({ listing }) {
  return (
    <div className="violation-card">
      <h4>商品ID: {listing.item_id}</h4>
      <div className="violation-reason">
        违规原因:{' '}
        <span className="reason-badge">
          {listing.detailed_reason_description}
        </span>
      </div>
      <div className="update-time">
        更新时间: {new Date(listing.update_time).toLocaleString()}
      </div>
    </div>
  );
}
```

## ✅ 验证清单

- [ ] 所有数值字段都有对应的描述字段
- [ ] 原始数值字段保持不变
- [ ] 描述字段命名规范（原字段名 + `_description`）
- [ ] 未知数值正确处理为 `Unknown(数值)` 格式
- [ ] 时间字段正确转换为UTC时间字符串
- [ ] 前端正确使用描述字段进行显示

这个映射转换功能大大提高了指标详情数据的可读性，让开发者和用户都能更容易理解各种订单状态、商品状态和违规原因的具体含义。
