# 时间格式标准化文档

## 📅 统一时间格式标准

所有接口返回的时间相关字段均采用 **UTC时间字符串格式**，遵循 ISO 8601 标准。

**重要说明**: Shopee API原始返回的时间字段为UTC时间戳格式（数字），我们的接口会自动将其转换为UTC时间字符串格式。

### 🎯 标准格式

```
YYYY-MM-DDTHH:mm:ssZ
```

- `YYYY`: 4位年份
- `MM`: 2位月份 (01-12)
- `DD`: 2位日期 (01-31)
- `T`: 日期和时间的分隔符
- `HH`: 2位小时 (00-23)
- `mm`: 2位分钟 (00-59)
- `ss`: 2位秒 (00-59)
- `Z`: UTC时区标识符

### 📋 时间字段清单

#### 1. 处罚相关时间字段

```json
{
  "ongoing_punishment": [
    {
      "start_date": "2024-01-15T00:00:00Z", // 处罚开始时间
      "end_date": "2024-01-22T23:59:59Z" // 处罚结束时间
    }
  ]
}
```

#### 2. 表现指标时间字段

```json
{
  "performance_overview": {
    "last_updated": "2024-01-20T10:30:00Z" // 最后更新时间
  }
}
```

#### 3. 处罚详情时间字段

```json
{
  "penalty_details": {
    "penalty_point_history": [
      {
        "date": "2024-01-15T09:30:00Z" // 处罚分变化时间
      }
    ],
    "punishment_history": [
      {
        "start_date": "2024-01-15T00:00:00Z", // 历史处罚开始时间
        "end_date": "2024-01-22T23:59:59Z" // 历史处罚结束时间
      }
    ],
    "listings_with_issues": [
      {
        "detected_date": "2024-01-15T09:00:00Z" // 问题商品检测时间
      }
    ],
    "late_orders": [
      {
        "order_date": "2024-01-15T10:30:00Z", // 订单日期
        "expected_ship_date": "2024-01-16T23:59:59Z", // 预期发货时间
        "actual_ship_date": "2024-01-19T08:20:00Z" // 实际发货时间
      }
    ]
  }
}
```

#### 4. 系统时间字段

```json
{
  "summary": {
    "query_time": "2024-01-20T10:30:00Z" // 查询时间
  }
}
```

### 🔧 前端处理建议

#### 1. JavaScript 时间处理

```javascript
// 解析UTC时间字符串
const utcTimeString = '2024-01-20T10:30:00Z';
const date = new Date(utcTimeString);

// 转换为本地时间显示
const localTimeString = date.toLocaleString();
console.log(localTimeString); // 根据用户时区显示

// 格式化显示
const formatOptions = {
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
  timeZoneName: 'short',
};
const formattedTime = date.toLocaleString('zh-CN', formatOptions);
```

#### 2. 时间计算示例

```javascript
// 计算处罚剩余时间
function getRemainingTime(endDateString) {
  const endDate = new Date(endDateString);
  const now = new Date();
  const diffMs = endDate - now;

  if (diffMs <= 0) {
    return { expired: true, message: '已过期' };
  }

  const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

  return {
    expired: false,
    days,
    hours,
    minutes,
    message: `还剩 ${days}天 ${hours}小时 ${minutes}分钟`,
  };
}

// 使用示例
const punishment = {
  end_date: '2024-01-22T23:59:59Z',
};
const remaining = getRemainingTime(punishment.end_date);
console.log(remaining.message);
```

#### 3. 时间显示组件

```jsx
function TimeDisplay({ utcTimeString, format = 'datetime' }) {
  const date = new Date(utcTimeString);

  const formatTime = (date, format) => {
    switch (format) {
      case 'date':
        return date.toLocaleDateString('zh-CN');
      case 'time':
        return date.toLocaleTimeString('zh-CN');
      case 'datetime':
        return date.toLocaleString('zh-CN');
      case 'relative':
        return getRelativeTime(date);
      default:
        return date.toLocaleString('zh-CN');
    }
  };

  return (
    <span title={date.toLocaleString('zh-CN')}>{formatTime(date, format)}</span>
  );
}

// 相对时间显示
function getRelativeTime(date) {
  const now = new Date();
  const diffMs = now - date;
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMinutes < 1) return '刚刚';
  if (diffMinutes < 60) return `${diffMinutes}分钟前`;
  if (diffHours < 24) return `${diffHours}小时前`;
  if (diffDays < 7) return `${diffDays}天前`;

  return date.toLocaleDateString('zh-CN');
}
```

### 🌍 时区处理注意事项

1. **服务端统一UTC**: 所有时间字段在服务端统一使用UTC时间
2. **前端本地化**: 前端根据用户时区进行本地化显示
3. **时间计算**: 所有时间计算基于UTC时间进行，避免时区问题
4. **用户体验**: 在界面上明确标识时区信息，避免用户混淆

### 🔄 时间戳转换处理

#### Shopee API时间戳转换

```javascript
// Shopee API原始返回（时间戳格式）
{
  "start_time": 1705305600,    // UTC时间戳
  "end_time": 1705392000
}

// 我们的接口返回（UTC字符串格式）
{
  "start_date": "2024-01-15T08:00:00Z",    // 自动转换
  "end_date": "2024-01-16T08:00:00Z"
}
```

#### 转换规则

- **输入**: UTC时间戳（数字格式）
- **输出**: UTC时间字符串（ISO 8601格式）
- **字段映射**: `start_time` → `start_date`, `end_time` → `end_date`
- **错误处理**: 无效时间戳返回 `null`

### ✅ 验证清单

- [ ] 所有时间字段都以 `Z` 结尾
- [ ] 时间格式符合 `YYYY-MM-DDTHH:mm:ssZ` 标准
- [ ] Shopee API时间戳正确转换为UTC字符串
- [ ] 字段名正确映射（start_time → start_date）
- [ ] 前端正确解析UTC时间字符串
- [ ] 时间显示考虑了用户时区
- [ ] 时间计算基于UTC时间进行

### 🔍 常见问题

**Q: 为什么使用UTC时间而不是本地时间？** A: UTC时间是全球统一的标准时间，避免了时区转换的复杂性和歧义性，特别适合跨时区的应用场景。

**Q: 前端如何处理不同时区的用户？** A: JavaScript的Date对象会自动根据用户的系统时区进行转换和显示，无需手动处理时区转换。

**Q: 如何确保时间格式的一致性？** A: 在后端统一使用ISO 8601格式输出，前端统一使用Date对象解析，确保格式的一致性。
