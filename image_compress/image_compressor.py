#基本调用方式：

#直接压缩图像对象：
# from PIL import Image
# from image_compressor import compress_image_data
# # 打开图像
# img = Image.open("input.jpg")
# # 压缩图像对象
# compressed_img, format = compress_image_data(img, quality=50, max_size=(1024, 1024))
# # 保存压缩后的图像
# compressed_img.save("output.jpg", format=format, quality=50, optimize=True)

#压缩本地图像文件：
# from image_compressor import compress_local_image
# # 基本用法 - 使用默认参数
# compress_local_image("input.jpg")  # 输出文件会自动命名为input_compr.jpg
# # 自定义参数
# compress_local_image("input.jpg", "output.jpg", quality=60, max_size=(800, 800))

#压缩图像字节数据
# from image_compressor import compress_image_bytes
# # 读取图像字节数据
# with open("input.jpg", "rb") as f:
#     image_bytes = f.read() 
# # 压缩字节数据
# compressed_bytes = compress_image_bytes(image_bytes, quality=60, max_size=(800, 800))
# # 保存压缩后的字节数据
# with open("output.jpg", "wb") as f:
#     f.write(compressed_bytes)



from PIL import Image
import os
import io

def compress_image_data(image_data, quality=50, max_size=None):
    """
    压缩图像数据
    
    参数:
        image_data: 输入图像数据(PIL.Image对象)
        quality: JPEG压缩质量 (1-100)，默认50
        max_size: 最大尺寸 (宽, 高)，默认None(保持原始尺寸)
    
    返回:
        压缩后的PIL.Image对象
    """
    # 获取原始格式
    original_format = image_data.format
    
    # 调整图像大小
    width, height = image_data.size
    if max_size:  # 只有当max_size不为None时才调整大小
        if width > max_size[0] or height > max_size[1]:
            ratio = min(max_size[0] / width, max_size[1] / height)
            new_size = (int(width * ratio), int(height * ratio))
            image_data = image_data.resize(new_size, Image.LANCZOS)
    
    return image_data, original_format

def compress_local_image(input_path, output_path=None, quality=50, max_size=None):
    """
    压缩本地图像并保存到指定路径
    
    参数:
        input_path: 输入图像的路径
        output_path: 输出图像的路径，如果为None则自动生成
        quality: JPEG压缩质量 (1-100)，默认50
        max_size: 最大尺寸 (宽, 高)，默认None(保持原始尺寸)
    
    返回:
        输出图像的路径
    """
    # 如果未指定输出路径，自动生成
    if output_path is None:
        base, ext = os.path.splitext(input_path)
        output_path = f"{base}_compr{ext}"
    
    # 确保输入输出路径在同一目录
    input_dir = os.path.dirname(os.path.abspath(input_path))
    output_path = os.path.join(input_dir, output_path)
    
    # 打开图像
    try:
        img = Image.open(input_path)
    except Exception as e:
        raise Exception(f"无法打开图像: {str(e)}")
    
    # 获取原始格式
    original_format = img.format
    if not original_format:
        original_format = os.path.splitext(input_path)[1][1:].upper()
        if original_format.lower() == 'jpg':
            original_format = 'JPEG'
    
    # 调用核心压缩函数
    img, _ = compress_image_data(img, quality, max_size)
    
    # 保存压缩后的图像
    try:
        if original_format == 'JPEG' or original_format == 'JPG':
            img.save(output_path, format=original_format, quality=quality, optimize=True)
        elif original_format == 'PNG':
            img.save(output_path, format=original_format, optimize=True, compress_level=9)
        else:
            img.save(output_path, format=original_format)
        
        print(f"图片已压缩并保存到: {output_path}")
        return output_path
    except Exception as e:
        raise Exception(f"保存压缩图像时出错: {str(e)}")

def compress_image_bytes(image_bytes, quality=50, max_size=None):
    """
    压缩图像字节数据并返回压缩后的字节数据
    
    参数:
        image_bytes: 输入图像的字节数据
        quality: JPEG压缩质量 (1-100)，默认50
        max_size: 最大尺寸 (宽, 高)，默认None(保持原始尺寸)
    
    返回:
        压缩后的图像字节数据
    """
    # 从字节数据加载图像
    try:
        img = Image.open(io.BytesIO(image_bytes))
    except Exception as e:
        raise Exception(f"无法从字节数据加载图像: {str(e)}")
    
    # 获取原始格式
    original_format = img.format
    if not original_format:
        original_format = 'JPEG'  # 默认为JPEG
    
    # 调用核心压缩函数
    img, original_format = compress_image_data(img, quality, max_size)
    
    # 保存压缩后的图像到字节流
    output_bytes = io.BytesIO()
    try:
        # 对于JPEG格式，使用quality参数
        if original_format == 'JPEG' or original_format == 'JPG':
            img.save(output_bytes, format=original_format, quality=quality, optimize=True)
        # 对于PNG格式，使用optimize和压缩级别
        elif original_format == 'PNG':
            img.save(output_bytes, format=original_format, optimize=True, compress_level=9)
        # 其他格式使用默认参数
        else:
            img.save(output_bytes, format=original_format)
        
        return output_bytes.getvalue()
    except Exception as e:
        raise Exception(f"保存压缩图像字节数据时出错: {str(e)}")

# 示例用法
if __name__ == "__main__":
    try:
        # 获取脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        input_path = os.path.join(script_dir, "1.jpeg")  # 避免使用中文文件名
        
        # 压缩图片
        compress_local_image(input_path, quality=50, max_size=None)
    except Exception as e:
        print(f"错误: {str(e)}")
        exit(1)